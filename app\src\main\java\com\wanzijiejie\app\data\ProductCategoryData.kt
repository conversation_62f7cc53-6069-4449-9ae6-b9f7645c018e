package com.wanzijiejie.app.data

import android.content.Context
import android.content.SharedPreferences
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * 商品分类数据管理
 */
object ProductCategoryData {
    // 饭堂商品列表
    val canteenItems = mutableListOf(
        "大饼", "小饼", "意面", "芝士",
        "鸡翅鸡腿", "中翅", "鱿鱼圈", "黑虎虾", "意面牛肉",
        "火腿肠", "海鲜酱", "牛排酱", "小酥肉", "烤鸭", "奥尔良鸡肉",
        "豆花", "火腿", "火腿丁", "火腿钉", "鸡翅腿", "黑椒牛排酱",
        "炒饭火腿钉", "火腿肠", "薯条", "鸡肉",
        "炸鸡翅鸡腿", "黑椒牛排汁", "鸡腿鸡翅", "逅浦黑胡椒汁", "逅浦黑椒汁",
        "小面包", "大面包", "三色粒", "反浦黑椒汁", "三色丁"
    )

    // 麻辣商品列表
    val spicyItems = mutableListOf(
        "无骨鸡爪", "香干", "魔芽干", "牙签牛肉", "萝卜皮",
        "鸡翅尖", "大辣片", "碎碎鸭", "牛板筋", "三角干",
        "木瓜丝", "木瓜条", "手撕豆皮", "火辣鸡爪", "柠檬鸡爪",
        "儿时豆皮", "小豆皮", "豆皮", "大辣片", "辣片", "翅尖"
    )

    // 冻货商品列表
    val frozenItems = mutableListOf(
        "榴莲肉", "榴莲泥", "烤翅", "鸡翅包饭", "一口肠", "披萨牛肉丁",
        "芽泥", "薄脆", "黑椒牛排", "菲力牛排", "战斧牛排", "雪花牛排",
        "香草西冷牛排", "T骨牛排", "黄油", "肉饼", "肉酱包", "大青虾",
        "玉米粒", "鸡柳", "鸡排", "墨鱼丸", "烤鸡", "甘梅薯条", "虾仁",
        "黑金鸡块", "洋葱圈", "炸鲜奶", "红糖糍粑", "笑脸薯饼", "薯格",
        "脆脆骨", "芽圆", "冻芒果", "美式黑椒牛排", "藤椒翅尖", "帕马臣芝士",
        "炒饭牛肉粒", "榴莲", "藤椒鸡腿", "蝶蝶虾", "藤椒鸡翅尖", "藤椒鸡翅尖",
        "冻榴莲肉", "小酥肉", "百香果原浆", "牛肉丁", "鸡米花", "牛肉粒",
        "对翅", "地瓜条", "玉米", "冻芒果肉", "炫辣牛肉", "香草牛排",
        "芽头", "大芽圆", "培根肉", "培根", "腊肠", "剔骨牛排", "炒饭牛肉",
        "炒饭牛肉", "花枝丸", "百香果汁", "BBQ", "奥尔良对翅", "香草西冷",
        "百香果酱", "t骨牛排", "t骨", "T骨"
    )

    // 其他商品列表
    val otherItems = mutableListOf(
        "金桔汁", "芒果果蔓", "橙汁", "果蜜", "黑白淡奶", "纯牛奶",
        "淡奶油", "厚椰乳", "椰浆", "橙子粒", "西柿粒", "红豆罐头",
        "仙草汁", "咖喱汁", "长吸管", "粗吸管", "大包番茄酱",
        "小包番茄酱", "珍珠", "西米", "面包糙", "面粉", "葡萄干",
        "饮料花生", "奶茶花生", "翠红辣椒粉", "葱香粉", "花生碎",
        "炸货花生碎", "绿茶", "红茶", "奶粉", "肉松", "咖喱膏",
        "老抽", "冰糖", "甘蔗糖", "意面黑椒汁", "熟红豆", "青稻爆珠",
        "马蹄爆爆珠", "安佳淡奶", "黄瓜", "青椒", "红椒", "蘑菇",
        "橙子", "孜然酱", "酱香酱", "洋葱", "餐纸", "意面打包盒", "logo披萨打包袋",
        "对翅打包盒", "鸡翅包饭打包盒", "柠檬", "香水柠檬", "小金桔",
        "香菇", "鸡蛋沙拉酱", "布丁杯", "布丁瓶", "喷射奶油", "西米爆爆珠",
        "奥利奥碎", "一次性勺子", "炒饭勺子", "意面叉子", "一次性叉子",
        "红椒丁", "红椒丝", "青椒丝", "草莓酱", "红豆", "绿豆", "糯米",
        "西柿粒", "芒果", "椰乳", "红茶叶", "板栗罐头", "番茄牛肉酱", "孜然粉",
        "老抽","荷兰芹", "植脂末", "一次性手套（小包装）", "披萨酱", "比萨酱",
        "黑胡椒汁", "披萨黑椒酱", "调饭牛肉酱", "蔑油", "橙汁罐", "安佳奶", "橙汁原浆",
        "布丁粉", "翠宏辣椒粉", "大酱盒", "水果麦片", "红豆罐头", "葡萄浓酱", "葡萄汁",
        "西米爆珠", "大番茄酱", "百利黑椒酱", "百利黑椒汁", "日期标签贴", "小保鲜膜",
        "菜单纸", "红枣", "洗洁精", "大躔料盒", "小躔料盒", "甘梅粉", "大酱料打包盒",
        "果粉", "8寸小花边纸", "安佳淡奶油", "鸡爪打包袋", "海鲜（虾仁炒饭）",
        "酱香味", "荷兰芹", "荷兰芹一把", "黄豆猪胑饭", "黄豆猪胑焖饭", "咖喱鸡肉酱包",
        "黄豆猪胑", "红烧牛肉", "红烧牛肉焖饭", "咖喱鸡肉饭", "咖喱鸡肉焖饭", "红烧牛腰饭",
        "咖喱鸡肉", "红烧牛腰", "日式咖喱鸡丁", "黄豆猪胑酱包", "黄豆焖猪胑", "咖喱鸡丁", "咖喱鸡肉焖饭酱包",
        "大葱子", "大葱粒", "葱籽", "小丸子", "汤圆", "葱子", "大葱", "黑芝麻"
    )

    // 特殊商品列表
    val specialItems = mutableListOf(
        "500打包杯",
        "500ml易拉罐打包杯",
        "700ml易拉罐打包杯",
        "350ml易拉罐打包杯",
        "350易拉罐打包杯",
        "易拉罐700打包瓶",
        "易拉罐500打包瓶",
        "易拉罐350打包瓶",
        "G7速溶黑咖啡",
        "G7速溶咖啡",
        "700打包杯",
        "8寸打包盒",
        "8寸披萨盒",
        "8寸垫子",
        "10寸垫子",
        "10寸打包盒",
        "10寸披萨盒",
        "10寸披萨打包盒",
        "500打包碗",
        "500打包盖",
        "350打包碗",
        "350打包盖",
        "500的甜品打包碗",
        "500的甜品打包盖",
        "500甜品打包碗",
        "500甜品打包盖",
        "350的甜品打包碗",
        "350的甜品打包盖",
        "350甜品打包碗",
        "350甜品打包盖",
        "350甜品碗",
        "350甜品盖",
        "500的甜品碗",
        "500的甜品盖",
        "G7咖啡"
    )

    /**
     * 保存商品分类数据到SharedPreferences
     */
    fun saveCategories(context: Context) {
        val sharedPreferences = context.getSharedPreferences("product_categories", Context.MODE_PRIVATE)
        val gson = Gson()

        // 保存各个分类的商品列表
        sharedPreferences.edit()
            .putString("canteen_items", gson.toJson(canteenItems))
            .putString("spicy_items", gson.toJson(spicyItems))
            .putString("frozen_items", gson.toJson(frozenItems))
            .putString("other_items", gson.toJson(otherItems))
            .putString("special_items", gson.toJson(specialItems))
            .apply()
    }

    /**
     * 从SharedPreferences加载商品分类数据
     */
    fun loadCategories(context: Context) {
        val sharedPreferences = context.getSharedPreferences("product_categories", Context.MODE_PRIVATE)
        val gson = Gson()
        val stringType = object : TypeToken<List<String>>() {}.type

        // 加载各个分类的商品列表
        val canteenJson = sharedPreferences.getString("canteen_items", null)
        if (canteenJson != null) {
            canteenItems.clear()
            canteenItems.addAll(gson.fromJson(canteenJson, stringType))
        }

        val spicyJson = sharedPreferences.getString("spicy_items", null)
        if (spicyJson != null) {
            spicyItems.clear()
            spicyItems.addAll(gson.fromJson(spicyJson, stringType))
        }

        val frozenJson = sharedPreferences.getString("frozen_items", null)
        if (frozenJson != null) {
            frozenItems.clear()
            frozenItems.addAll(gson.fromJson(frozenJson, stringType))
        }

        val otherJson = sharedPreferences.getString("other_items", null)
        if (otherJson != null) {
            otherItems.clear()
            otherItems.addAll(gson.fromJson(otherJson, stringType))
        }

        val specialJson = sharedPreferences.getString("special_items", null)
        if (specialJson != null) {
            specialItems.clear()
            specialItems.addAll(gson.fromJson(specialJson, stringType))
        }
    }

    /**
     * 添加商品到指定分类
     */
    fun addItemToCategory(item: String, category: String, context: Context) {
        when (category) {
            "饭堂" -> if (!canteenItems.contains(item)) canteenItems.add(item)
            "麻辣" -> if (!spicyItems.contains(item)) spicyItems.add(item)
            "冻货" -> if (!frozenItems.contains(item)) frozenItems.add(item)
            "其他" -> if (!otherItems.contains(item)) otherItems.add(item)
            "特殊" -> if (!specialItems.contains(item)) specialItems.add(item)
        }
        saveCategories(context)
    }

    /**
     * 获取商品所属的分类
     */
    fun getCategoryForItem(item: String): String {
        // 先尝试处理带有数字的商品名称
        val match = Regex("^(.+?)(\\d+.*)$").find(item)
        val trimmedItem = if (match != null) {
            // 如果商品名称带有数字，只取前面的部分
            match.groupValues[1].trim()
        } else {
            // 否则取整个商品名称
            item.trim()
        }

        // 只使用精确匹配
        if (canteenItems.contains(trimmedItem)) return "饭堂"
        if (spicyItems.contains(trimmedItem)) return "麻辣"
        if (frozenItems.contains(trimmedItem)) return "冻货"
        if (otherItems.contains(trimmedItem)) return "其他"
        if (specialItems.contains(trimmedItem)) return "其他" // 特殊商品归类到"其他"

        // 如果没有精确匹配，返回未分类
        return "未分类"
    }

    /**
     * 检查商品名中是否有两个相同的字符
     */
    private fun hasTwoIdenticalCharacters(itemName: String): Boolean {
        val charCount = mutableMapOf<Char, Int>()
        for (char in itemName) {
            charCount[char] = (charCount[char] ?: 0) + 1
            if (charCount[char]!! >= 2) {
                return true
            }
        }
        return false
    }

    /**
     * 根据商品名的特定规则确定分类到"饭堂"或"冻货"
     */
    private fun checkCategoryForCanteen(itemName: String): Boolean {
        // 如果相同字符出现在前两个字符中，则分类到"饭堂"
        val chars = itemName.toCharArray()
        val firstTwoChars = chars.take(2)
        val charSet = firstTwoChars.toSet()

        for (char in charSet) {
            if (firstTwoChars.count { it == char } >= 2) {
                return true
            }
        }
        return false
    }
}
