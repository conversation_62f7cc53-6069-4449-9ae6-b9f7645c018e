package com.wanzijiejie.app.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.clickable
import androidx.compose.ui.draw.rotate
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Share
import androidx.compose.material.icons.filled.ShoppingCart
import androidx.compose.material.icons.filled.Done
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.LaunchedEffect
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.wanzijiejie.app.data.ReportHistoryManager
import com.wanzijiejie.app.services.MusicPlayerService
import com.wanzijiejie.app.ui.navigation.AppDestinations
import com.wanzijiejie.app.R
import com.wanzijiejie.app.ui.theme.丸子姐姐报表Theme
import android.Manifest
import android.content.ContentValues
import android.content.Intent
import android.graphics.Bitmap
import android.content.ClipboardManager
import android.content.Context
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.provider.MediaStore
import android.view.View
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import com.wanzijiejie.app.data.ProductCategoryData
import com.wanzijiejie.app.utils.ContentParser
import com.wanzijiejie.app.utils.ReportTableGenerator
import java.io.File
import java.io.FileOutputStream
import java.io.OutputStream
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.UUID

// 这些函数已移至 ContentParser 类

// 商品数据类
data class Product(
    var name1: String = "",
    var quantity1: String = "",
    var name2: String = "",
    var quantity2: String = ""
)

// 区域数据类
data class Area(
    val name: String,
    val products: MutableList<Product> = mutableStateListOf()
)

// 保存的报表数据
data class SavedReport(
    val date: String,
    val areas: List<Area>
)

// 全局保存的报表列表
val savedReports = mutableListOf<SavedReport>()

// 静态变量，用于存储剪贴板内容和应用设置
// 注意：这个变量在应用程序的生命周期内保持不变
object AutoOrderHelper {
    var clipboardContent: String = ""
    var needAutoPaste: Boolean = false
    var autoClickOrderButton: Boolean = false // 标记是否需要自动点击自动报货按钮

    // 用于存储用户偏好的键名
    private const val PREF_NAME = "auto_order_settings"
    private const val KEY_AUTO_ORDER_ON_STARTUP = "auto_order_on_startup"

    // 获取是否在启动时自动报货的设置
    fun getAutoOrderOnStartup(context: Context): Boolean {
        val prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
        return prefs.getBoolean(KEY_AUTO_ORDER_ON_STARTUP, false) // 默认为关闭
    }

    // 保存是否在启动时自动报货的设置
    fun setAutoOrderOnStartup(context: Context, enabled: Boolean) {
        val prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
        prefs.edit().putBoolean(KEY_AUTO_ORDER_ON_STARTUP, enabled).apply()
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ReportScreen(
    dateMillis: Long,
    navController: NavController = rememberNavController(),
    modifier: Modifier = Modifier,
    musicPlayerService: MusicPlayerService? = null
) {
    // 将时间戳转换为日期
    val calendar = Calendar.getInstance()
    calendar.timeInMillis = dateMillis
    val date = calendar.time

    // 格式化日期
    val dateFormat = SimpleDateFormat("yyyy年MM月dd日", Locale.getDefault())
    val formattedDate = dateFormat.format(date)

    // 获取区域名称字符串
    val canteenName = stringResource(R.string.canteen)
    val spicyName = stringResource(R.string.spicy)
    val frozenName = stringResource(R.string.frozen)
    val othersName = stringResource(R.string.others)

    // 创建四个区域的数据
    val areas = remember {
        mutableStateListOf(
            Area(name = canteenName),
            Area(name = spicyName),
            Area(name = frozenName),
            Area(name = othersName)
        )
    }

    // 获取当前上下文
    val context = LocalContext.current

    // 获取当前视图
    val reportView = LocalView.current

    // 创建协程作用域
    val scope = rememberCoroutineScope()

    // 权限请求状态
    var hasStoragePermission by remember {
        mutableStateOf(
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED ||
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q
        )
    }

    // 将topPasteContent变量提升到外部作用域，使其在自动报货按钮中可见
    val topPasteContentRef = remember { mutableStateOf("") }
    val updateTopPasteContent: (String) -> Unit = { newValue ->
        topPasteContentRef.value = newValue
    }

    // 检查是否需要自动粘贴和解析
    val needAutoPaste = remember { mutableStateOf(AutoOrderHelper.needAutoPaste) }
    val clipboardContent = remember { mutableStateOf(AutoOrderHelper.clipboardContent) }

    // 如果需要自动粘贴和解析，则在页面加载后执行
    // 使用needAutoPaste作为LaunchedEffect的key，确保只在需要时执行
    LaunchedEffect(needAutoPaste.value) {
        // 只在需要自动粘贴和内容非空时执行
        if (needAutoPaste.value && clipboardContent.value.isNotBlank()) {
            // 延迟1500毫秒，确保页面完全加载
            delay(1500)

            // 先清空所有区域的商品，确保覆盖模式正常工作
            areas.forEach { area ->
                area.products.clear()
            }

            // 将剪贴板内容设置到一键粘贴区域
            updateTopPasteContent(clipboardContent.value)

            // 模拟点击“解析并填充”按钮
            // 使用ContentParser处理粘贴内容（覆盖模式）
            // 首先加载商品分类数据
            ProductCategoryData.loadCategories(context)

            // 创建一个状态变量，用于跟踪分类过程是否完成
            var isClassifying = false

            // 处理粘贴内容（覆盖模式）
            val success = ContentParser.processPastedContent(
                content = clipboardContent.value,
                areas = areas,
                context = context,
                mode = 0, // 覆盖模式
                onBatchClassifyStart = {
                    // 开始批量分类
                    isClassifying = true
                },
                onBatchClassifyComplete = {
                    // 完成批量分类
                    isClassifying = false

                    // 清空粘贴区域
                    updateTopPasteContent("")

                    Toast.makeText(context, context.getString(R.string.parse_success), Toast.LENGTH_SHORT).show()
                }
            )

            if (success && !isClassifying) {
                // 如果没有未分类的商品，直接清空粘贴区域
                updateTopPasteContent("")

                Toast.makeText(context, context.getString(R.string.parse_success), Toast.LENGTH_SHORT).show()
            }

            // 重置自动粘贴标志，避免再次进入页面时自动粘贴
            AutoOrderHelper.needAutoPaste = false
            AutoOrderHelper.clipboardContent = ""
            needAutoPaste.value = false
        }
    }

    // 权限请求启动器
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        hasStoragePermission = isGranted
        if (isGranted) {
            // 权限获取后的操作
            val tableGenerator = ReportTableGenerator(context)
            val reportTitle = context.getString(R.string.report_title)

            // 生成表格视图
            val tableView = tableGenerator.generateReportTableView(
                date = formattedDate,
                title = reportTitle,
                areas = areas.toList()
            )

            // 截取视图并保存
            tableGenerator.captureViewAndSave(
                view = tableView,
                callback = object : ReportTableGenerator.ScreenshotCallback {
                    override fun onScreenshotTaken(success: Boolean, bitmap: Bitmap?) {
                        if (success) {
                            Toast.makeText(context, context.getString(R.string.image_saved), Toast.LENGTH_SHORT).show()
                        } else {
                            Toast.makeText(context, context.getString(R.string.save_failed), Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            )
        } else {
            Toast.makeText(context, context.getString(R.string.permission_required), Toast.LENGTH_SHORT).show()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.report_title)) },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    // 历史记录按钮
                    IconButton(onClick = { navController.navigate(AppDestinations.HISTORY_ROUTE) }) {
                        Text(stringResource(R.string.history_records))
                    }
                }
            )
        }
    ) { paddingValues ->
        Surface(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues),
            color = MaterialTheme.colorScheme.background
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 报表标题
                Text(
                    text = stringResource(R.string.report_title),
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(8.dp))

                // 报表日期
                Text(
                    text = formattedDate,
                    style = MaterialTheme.typography.titleMedium,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 顶部一键粘贴区域
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        // 粘贴区域标题
                        Text(
                            text = stringResource(R.string.paste_area),
                            style = MaterialTheme.typography.titleLarge,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.fillMaxWidth()
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        // 粘贴内容输入框
                        OutlinedTextField(
                            value = topPasteContentRef.value,
                            onValueChange = { updateTopPasteContent(it) },
                            label = { Text(stringResource(R.string.paste_hint)) },
                            modifier = Modifier.fillMaxWidth(),
                            minLines = 3,
                            maxLines = 5
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        // 按钮行
                        Column(
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            // 第一行按钮
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                // 清空按钮
                                Button(
                                    onClick = {
                                    // 清空所有区域的商品
                                    areas.forEach { area ->
                                        area.products.clear()
                                    }

                                    // 清空粘贴区域
                                    updateTopPasteContent("")

                                    Toast.makeText(context, context.getString(R.string.clear_all_success), Toast.LENGTH_SHORT).show()
                                    },
                                    modifier = Modifier.weight(1f)
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Clear,
                                        contentDescription = stringResource(R.string.clear_all)
                                    )
                                    Spacer(modifier = Modifier.width(4.dp))
                                    Text(stringResource(R.string.clear_all))
                                }

                                Spacer(modifier = Modifier.width(8.dp))

                                // 解析并填充按钮
                                Button(
                                    onClick = {
                                    if (topPasteContentRef.value.isBlank()) {
                                        Toast.makeText(context, context.getString(R.string.parse_empty), Toast.LENGTH_SHORT).show()
                                        return@Button
                                    }

                                    // 先清空所有区域的商品，确保覆盖模式正常工作
                                    areas.forEach { area ->
                                        area.products.clear()
                                    }

                                    // 使用ContentParser处理粘贴内容（覆盖模式）
                                    // 首先加载商品分类数据
                                    ProductCategoryData.loadCategories(context)

                                    // 创建一个状态变量，用于跟踪分类过程是否完成
                                    var isClassifying = false

                                    // 处理粘贴内容（覆盖模式）
                                    val success = ContentParser.processPastedContent(
                                        content = topPasteContentRef.value,
                                        areas = areas,
                                        context = context,
                                        mode = 0, // 覆盖模式
                                        onBatchClassifyStart = {
                                            // 开始批量分类
                                            isClassifying = true
                                        },
                                        onBatchClassifyComplete = {
                                            // 完成批量分类
                                            isClassifying = false

                                            // 清空粘贴区域
                                            updateTopPasteContent("")

                                            Toast.makeText(context, context.getString(R.string.parse_success), Toast.LENGTH_SHORT).show()
                                        }
                                    )

                                    if (success && !isClassifying) {
                                        // 如果没有未分类的商品，直接清空粘贴区域
                                        updateTopPasteContent("")

                                        Toast.makeText(context, context.getString(R.string.parse_success), Toast.LENGTH_SHORT).show()
                                    }
                                    },
                                    modifier = Modifier.weight(1f)
                                ) {
                                    Row {
                                        Icon(
                                            imageVector = Icons.Default.Done,
                                            contentDescription = stringResource(R.string.paste_button)
                                        )
                                        Spacer(modifier = Modifier.width(4.dp))
                                        Text(
                                            text = stringResource(R.string.paste_button),
                                            style = MaterialTheme.typography.bodyMedium
                                        )
                                    }
                                }

                            }

                            Spacer(modifier = Modifier.height(8.dp))

                            // 继续添加按钮
                            Button(
                                onClick = {
                                    if (topPasteContentRef.value.isBlank()) {
                                        Toast.makeText(context, context.getString(R.string.parse_empty), Toast.LENGTH_SHORT).show()
                                        return@Button
                                    }

                                    // 使用ContentParser处理粘贴内容（继续添加模式）
                                    // 首先加载商品分类数据
                                    ProductCategoryData.loadCategories(context)

                                    // 创建一个状态变量，用于跟踪分类过程是否完成
                                    var isClassifying = false

                                    // 处理粘贴内容（继续添加模式）
                                    val success = ContentParser.processPastedContent(
                                        content = topPasteContentRef.value,
                                        areas = areas,
                                        context = context,
                                        mode = 1, // 继续添加模式
                                        onBatchClassifyStart = {
                                            // 开始批量分类
                                            isClassifying = true
                                        },
                                        onBatchClassifyComplete = {
                                            // 完成批量分类
                                            isClassifying = false

                                            // 清空粘贴区域
                                            updateTopPasteContent("")

                                            Toast.makeText(context, context.getString(R.string.parse_success), Toast.LENGTH_SHORT).show()
                                        }
                                    )

                                    if (success && !isClassifying) {
                                        // 如果没有未分类的商品，直接清空粘贴区域
                                        updateTopPasteContent("")

                                        Toast.makeText(context, context.getString(R.string.parse_success), Toast.LENGTH_SHORT).show()
                                    }
                                },
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Row {
                                    Icon(
                                        imageVector = Icons.Default.Add,
                                        contentDescription = stringResource(R.string.continue_adding)
                                    )
                                    Spacer(modifier = Modifier.width(4.dp))
                                    Text(
                                        text = stringResource(R.string.continue_adding),
                                        style = MaterialTheme.typography.bodyMedium
                                    )
                                }
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // 自动报货按钮
                Button(
                    onClick = {
                        // 尝试从剪贴板获取内容
                        val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                        val clipData = clipboardManager.primaryClip

                        // 如果剪贴板有内容，则将其粘贴到一键粘贴区域
                        if (clipData != null && clipData.itemCount > 0) {
                            val clipText = clipData.getItemAt(0).text.toString()

                            // 先清空所有区域的商品，确保覆盖模式正常工作
                            areas.forEach { area ->
                                area.products.clear()
                            }

                            // 将剪贴板内容设置到一键粘贴区域
                            updateTopPasteContent(clipText)

                            // 模拟点击“解析并填充”按钮
                            // 使用ContentParser处理粘贴内容（覆盖模式）
                            // 首先加载商品分类数据
                            ProductCategoryData.loadCategories(context)

                            // 创建一个状态变量，用于跟踪分类过程是否完成
                            var isClassifying = false

                            // 处理粘贴内容（覆盖模式）
                            val success = ContentParser.processPastedContent(
                                content = clipText,
                                areas = areas,
                                context = context,
                                mode = 0, // 覆盖模式
                                onBatchClassifyStart = {
                                    // 开始批量分类
                                    isClassifying = true
                                },
                                onBatchClassifyComplete = {
                                    // 完成批量分类
                                    isClassifying = false

                                    // 清空粘贴区域
                                    updateTopPasteContent("")

                                    Toast.makeText(context, context.getString(R.string.parse_success), Toast.LENGTH_SHORT).show()
                                }
                            )

                            if (success && !isClassifying) {
                                // 如果没有未分类的商品，直接清空粘贴区域
                                updateTopPasteContent("")

                                Toast.makeText(context, context.getString(R.string.parse_success), Toast.LENGTH_SHORT).show()
                            }
                        } else {
                            Toast.makeText(context, context.getString(R.string.clipboard_empty), Toast.LENGTH_SHORT).show()
                        }
                    },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Row {
                        Icon(
                            imageVector = Icons.Default.ShoppingCart,
                            contentDescription = stringResource(R.string.auto_order)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(stringResource(R.string.auto_order))
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // 截图进度状态
                var isScreenshotInProgress by remember { mutableStateOf(false) }

                // 提交并截图按钮
                Button(
                    onClick = {
                        // 检查存储权限
                        if (hasStoragePermission) {
                            // 设置截图进度状态为true
                            isScreenshotInProgress = true

                            // 显示正在处理的提示
                            Toast.makeText(context, "正在生成截图，请稍候...", Toast.LENGTH_SHORT).show()

                            // 生成报表并截图
                            val tableGenerator = ReportTableGenerator(context)
                            val reportTitle = context.getString(R.string.report_title)

                            // 生成表格视图
                            val tableView = tableGenerator.generateReportTableView(
                                date = formattedDate,
                                title = reportTitle,
                                areas = areas.toList()
                            )

                            // 截取视图并保存
                            tableGenerator.captureViewAndSave(
                                view = tableView,
                                callback = object : ReportTableGenerator.ScreenshotCallback {
                                    override fun onScreenshotTaken(success: Boolean, bitmap: Bitmap?) {
                                        // 设置截图进度状态为false
                                        isScreenshotInProgress = false

                                        if (success && bitmap != null) {
                                            // 保存到历史记录
                                            val historyManager = ReportHistoryManager(context)
                                            val report = historyManager.saveReport(
                                                date = formattedDate,
                                                title = reportTitle,
                                                areas = areas.toList(),
                                                bitmap = bitmap
                                            )

                                            Toast.makeText(context, context.getString(R.string.save_success), Toast.LENGTH_SHORT).show()

                                            // 分享到微信，但不中断音乐播放
                                            shareImageToWeChat(bitmap, context, musicPlayerService)

                                            // 跳转到历史记录页面，但不中断音乐播放

                                            navController.navigate(AppDestinations.HISTORY_ROUTE) {
                                                popUpTo(AppDestinations.REPORT_ROUTE) { inclusive = false }
                                            }
                                        } else {
                                            Toast.makeText(context, context.getString(R.string.save_failed), Toast.LENGTH_SHORT).show()
                                        }
                                    }
                                }
                            )
                        } else {
                            // 请求存储权限
                            permissionLauncher.launch(Manifest.permission.WRITE_EXTERNAL_STORAGE)
                        }
                    },
                    modifier = Modifier.align(Alignment.CenterHorizontally),
                    // 当截图进行中时禁用按钮
                    enabled = !isScreenshotInProgress
                ) {
                    if (isScreenshotInProgress) {
                        // 显示加载指示器
                        androidx.compose.material3.CircularProgressIndicator(
                            modifier = Modifier.width(16.dp).height(16.dp),
                            color = Color.White,
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("处理中...")
                    } else {
                        Icon(
                            imageVector = Icons.Default.Done,
                            contentDescription = stringResource(R.string.submit_and_screenshot)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(stringResource(R.string.submit_and_screenshot))
                    }
                }

                // 区域卡片
                areas.forEach { area ->
                    AreaCard(
                        area = area,
                        onAddProduct = {
                            // 添加新的商品行
                            area.products.add(Product())
                        }
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                }

                Spacer(modifier = Modifier.height(24.dp))

                // 底部一键粘贴区域
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        // 粘贴区域标题
                        Text(
                            text = stringResource(R.string.paste_area),
                            style = MaterialTheme.typography.titleLarge,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.fillMaxWidth()
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        // 粘贴内容输入框
                        var pasteContent by remember { mutableStateOf("") }

                        OutlinedTextField(
                            value = pasteContent,
                            onValueChange = { pasteContent = it },
                            label = { Text(stringResource(R.string.paste_hint)) },
                            modifier = Modifier.fillMaxWidth(),
                            minLines = 3,
                            maxLines = 5
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        // 按钮行
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.End
                        ) {
                            // 解析并填充按钮
                            Button(
                                onClick = {
                                    if (pasteContent.isBlank()) {
                                        Toast.makeText(context, context.getString(R.string.parse_empty), Toast.LENGTH_SHORT).show()
                                        return@Button
                                    }

                                    // 使用ContentParser处理粘贴内容（覆盖模式）
                                    // 首先加载商品分类数据
                                    ProductCategoryData.loadCategories(context)

                                    // 创建一个状态变量，用于跟踪分类过程是否完成
                                    var isClassifying = false

                                    // 处理粘贴内容（覆盖模式）
                                    val success = ContentParser.processPastedContent(
                                        content = pasteContent,
                                        areas = areas,
                                        context = context,
                                        mode = 0, // 覆盖模式
                                        onBatchClassifyStart = {
                                            // 开始批量分类
                                            isClassifying = true
                                        },
                                        onBatchClassifyComplete = {
                                            // 完成批量分类
                                            isClassifying = false

                                            // 清空粘贴区域
                                            pasteContent = ""

                                            Toast.makeText(context, context.getString(R.string.parse_success), Toast.LENGTH_SHORT).show()
                                        }
                                    )

                                    if (success && !isClassifying) {
                                        // 如果没有未分类的商品，直接清空粘贴区域
                                        pasteContent = ""

                                        Toast.makeText(context, context.getString(R.string.parse_success), Toast.LENGTH_SHORT).show()
                                    }
                                }
                            ) {
                                Text(stringResource(R.string.paste_button))
                            }

                            Spacer(modifier = Modifier.width(8.dp))

                            // 继续添加按钮
                            Button(
                                onClick = {
                                    if (pasteContent.isBlank()) {
                                        Toast.makeText(context, context.getString(R.string.parse_empty), Toast.LENGTH_SHORT).show()
                                        return@Button
                                    }

                                    // 使用ContentParser处理粘贴内容（继续添加模式）
                                    // 首先加载商品分类数据
                                    ProductCategoryData.loadCategories(context)

                                    // 创建一个状态变量，用于跟踪分类过程是否完成
                                    var isClassifying = false

                                    // 处理粘贴内容（继续添加模式）
                                    val success = ContentParser.processPastedContent(
                                        content = pasteContent,
                                        areas = areas,
                                        context = context,
                                        mode = 1, // 继续添加模式
                                        onBatchClassifyStart = {
                                            // 开始批量分类
                                            isClassifying = true
                                        },
                                        onBatchClassifyComplete = {
                                            // 完成批量分类
                                            isClassifying = false

                                            // 清空粘贴区域
                                            pasteContent = ""

                                            Toast.makeText(context, context.getString(R.string.parse_success), Toast.LENGTH_SHORT).show()
                                        }
                                    )

                                    if (success && !isClassifying) {
                                        // 如果没有未分类的商品，直接清空粘贴区域
                                        pasteContent = ""

                                        Toast.makeText(context, context.getString(R.string.parse_success), Toast.LENGTH_SHORT).show()
                                    }
                                }
                            ) {
                                Text(stringResource(R.string.continue_adding))
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AreaCard(
    area: Area,
    onAddProduct: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 区域标题
            Text(
                text = area.name,
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(16.dp))

            // 商品列表
            if (area.products.isEmpty()) {
                // 如果商品列表为空，显示提示信息
                Text(
                    text = "暂无商品，请点击“添加商品”按钮添加",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 16.dp)
                )
            } else {
                // 显示商品列表
                area.products.forEachIndexed { index, product ->
                    ProductRow(
                        product = product,
                        onNameChange = { area.products[index] = product.copy(name1 = it) },
                        onQuantityChange = { area.products[index] = product.copy(quantity1 = it) },
                        onName2Change = { area.products[index] = product.copy(name2 = it) },
                        onQuantity2Change = { area.products[index] = product.copy(quantity2 = it) },
                        onClear = {
                            // 清空当前行的商品名称和数量
                            area.products[index] = Product()
                        },
                        onRemove = {
                            // 移除当前商品行
                            area.products.removeAt(index)
                        }
                    )

                    Spacer(modifier = Modifier.height(8.dp))
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 添加商品按钮
            Button(
                onClick = onAddProduct,
                modifier = Modifier.align(Alignment.End)
            ) {
                Text(stringResource(R.string.add_product))
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProductRow(
    product: Product,
    onNameChange: (String) -> Unit,
    onQuantityChange: (String) -> Unit,
    onName2Change: (String) -> Unit,
    onQuantity2Change: (String) -> Unit,
    onClear: () -> Unit = {},
    onRemove: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        // 第一行
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 第一个商品
            Row(modifier = Modifier.weight(1f)) {
                // 商品名
                Column(modifier = Modifier.weight(0.6f)) {

                    OutlinedTextField(
                        value = product.name1,
                        onValueChange = onNameChange,
                        modifier = Modifier
                            .fillMaxWidth(), // 使用内容高度，自适应增长
                        textStyle = TextStyle(
                            fontSize = 16.sp
                        ),
                        maxLines = Int.MAX_VALUE, // 不限制行数，允许自由换行
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = MaterialTheme.colorScheme.primary,
                            unfocusedBorderColor = MaterialTheme.colorScheme.outline,
                            focusedTextColor = MaterialTheme.colorScheme.onSurface,
                            unfocusedTextColor = MaterialTheme.colorScheme.onSurface
                        ),
                        label = null
                    )
                }

                Spacer(modifier = Modifier.width(8.dp))

                // 数量
                Column(modifier = Modifier.weight(0.4f)) {

                    OutlinedTextField(
                        value = product.quantity1,
                        onValueChange = onQuantityChange,
                        modifier = Modifier
                            .fillMaxWidth(), // 使用内容高度，自适应增长
                        textStyle = TextStyle(
                            fontSize = 16.sp
                        ),
                        maxLines = Int.MAX_VALUE, // 不限制行数，允许自由换行
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = MaterialTheme.colorScheme.primary,
                            unfocusedBorderColor = MaterialTheme.colorScheme.outline,
                            focusedTextColor = MaterialTheme.colorScheme.onSurface,
                            unfocusedTextColor = MaterialTheme.colorScheme.onSurface
                        ),
                        label = null
                    )
                }
            }

            Spacer(modifier = Modifier.width(16.dp))

            // 第二个商品
            Row(modifier = Modifier.weight(1f)) {
                // 商品名
                Column(modifier = Modifier.weight(0.6f)) {

                    OutlinedTextField(
                        value = product.name2,
                        onValueChange = onName2Change,
                        modifier = Modifier
                            .fillMaxWidth(), // 使用内容高度，自适应增长
                        textStyle = TextStyle(
                            fontSize = 16.sp
                        ),
                        maxLines = Int.MAX_VALUE, // 不限制行数，允许自由换行
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = MaterialTheme.colorScheme.primary,
                            unfocusedBorderColor = MaterialTheme.colorScheme.outline,
                            focusedTextColor = MaterialTheme.colorScheme.onSurface,
                            unfocusedTextColor = MaterialTheme.colorScheme.onSurface
                        ),
                        label = null
                    )
                }

                Spacer(modifier = Modifier.width(8.dp))

                // 数量
                Column(modifier = Modifier.weight(0.4f)) {

                    OutlinedTextField(
                        value = product.quantity2,
                        onValueChange = onQuantity2Change,
                        modifier = Modifier
                            .fillMaxWidth(), // 使用内容高度，自适应增长
                        textStyle = TextStyle(
                            fontSize = 16.sp
                        ),
                        maxLines = Int.MAX_VALUE, // 不限制行数，允许自由换行
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = MaterialTheme.colorScheme.primary,
                            unfocusedBorderColor = MaterialTheme.colorScheme.outline,
                            focusedTextColor = MaterialTheme.colorScheme.onSurface,
                            unfocusedTextColor = MaterialTheme.colorScheme.onSurface
                        ),
                        label = null
                    )
                }
            }
        }

        // 操作按钮
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 8.dp),
            horizontalArrangement = Arrangement.End
        ) {
            Button(
                onClick = onClear,
                modifier = Modifier.padding(end = 8.dp)
            ) {
                Text(stringResource(R.string.clear))
            }

            Button(
                onClick = onRemove
            ) {
                Text(stringResource(R.string.remove))
            }
        }
    }
}

/**
 * 分享图片到微信
 * @param bitmap 要分享的位图
 * @param context 上下文
 * @param musicPlayerService 音乐播放服务，用于防止音乐被中断
 */
private fun shareImageToWeChat(bitmap: Bitmap, context: Context, musicPlayerService: MusicPlayerService? = null) {
    try {
        // 显示分享中提示
        Toast.makeText(context, context.getString(R.string.sharing), Toast.LENGTH_SHORT).show()

        // 创建临时文件
        val cachePath = java.io.File(context.cacheDir, "images")
        cachePath.mkdirs()
        val file = java.io.File(cachePath, "shared_image.jpg")

        // 将位图保存到临时文件
        val outputStream = java.io.FileOutputStream(file)
        bitmap.compress(Bitmap.CompressFormat.JPEG, 100, outputStream)
        outputStream.flush()
        outputStream.close()

        // 创建FileProvider URI
        val contentUri = androidx.core.content.FileProvider.getUriForFile(
            context,
            context.packageName + ".provider",
            file
        )

        // 创建分享Intent
        val intent = Intent(Intent.ACTION_SEND).apply {
            type = "image/jpeg"
            putExtra(Intent.EXTRA_STREAM, contentUri)
            // 不指定特定应用，让用户选择
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            // 添加FLAG_ACTIVITY_NEW_TASK标志，确保在新任务中启动，不影响当前应用
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }

        // 确保音乐继续播放，不需要特殊处理

        // 创建选择器Intent
        val chooserIntent = Intent.createChooser(intent, context.getString(R.string.share_to_wechat))

        // 检查是否有应用可以处理分享操作
        if (intent.resolveActivity(context.packageManager) != null) {
            // 设置外部活动状态为true，防止音乐被中断
            musicPlayerService?.setExternalActivityRunning(true)

            // 启动分享选择器
            context.startActivity(chooserIntent)

            // 显示成功提示
            Toast.makeText(context, context.getString(R.string.share_success), Toast.LENGTH_SHORT).show()
        } else {
            // 没有应用可以处理分享
            Toast.makeText(context, context.getString(R.string.share_failed), Toast.LENGTH_SHORT).show()
        }
    } catch (e: Exception) {
        e.printStackTrace()
        Toast.makeText(context, context.getString(R.string.share_failed), Toast.LENGTH_SHORT).show()
    }
}

@Preview(showBackground = true)
@Composable
fun ReportScreenPreview() {
    丸子姐姐报表Theme {
        ReportScreen(
            dateMillis = System.currentTimeMillis(),
            musicPlayerService = null // 预览时不需要音乐服务
        )
    }
}
