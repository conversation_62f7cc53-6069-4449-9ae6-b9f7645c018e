package com.wanzijiejie.app.ui.screens

import android.content.Context
import android.content.Intent
import android.graphics.BitmapFactory
import android.widget.Toast
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.clickable
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Done
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material.icons.filled.Share
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.foundation.layout.size
import kotlinx.coroutines.delay
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.wanzijiejie.app.R
import com.wanzijiejie.app.data.ReportHistoryManager
import com.wanzijiejie.app.services.MusicPlayerService
import java.io.File

/**
 * 历史记录屏幕
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HistoryScreen(
    navController: NavController = rememberNavController(),
    modifier: Modifier = Modifier,
    musicPlayerService: MusicPlayerService? = null
) {
    val context = LocalContext.current

    // 获取历史记录
    val historyManager = ReportHistoryManager(context)
    val historyReports = remember { historyManager.loadReports() }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.history_records)) },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )
        }
    ) { paddingValues ->
        if (historyReports.isEmpty()) {
            // 如果没有历史记录，显示提示信息
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                Text(stringResource(R.string.no_history_records))
            }
        } else {
            // 显示历史记录列表
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                items(historyReports) { report ->
                    HistoryReportCard(
                        report = report,
                        context = context,
                        onDelete = {
                            // 删除历史记录
                            historyManager.deleteReport(report.id)
                            // 刷新列表
                            historyReports.remove(report)
                        },
                        musicPlayerService = musicPlayerService
                    )
                }
            }
        }
    }
}

/**
 * 历史报表卡片
 */
@Composable
fun HistoryReportCard(
    report: ReportHistory,
    context: Context,
    onDelete: () -> Unit,
    musicPlayerService: MusicPlayerService? = null
) {
    var expanded by remember { mutableStateOf(false) }
    var showDeleteDialog by remember { mutableStateOf(false) }
    var isDeleting by remember { mutableStateOf(false) }

    // 确认删除对话框
    if (showDeleteDialog) {
        AlertDialog(
            onDismissRequest = {
                if (!isDeleting) {
                    showDeleteDialog = false
                }
            },
            title = { Text(stringResource(R.string.confirm_delete)) },
            text = {
                if (isDeleting) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(48.dp)
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(stringResource(R.string.deleting))
                    }
                } else {
                    Text(stringResource(R.string.confirm_delete_history))
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        isDeleting = true
                    },
                    enabled = !isDeleting
                ) {
                    Text(stringResource(R.string.confirm))
                }

                // 使用协程在后台执行删除操作
                if (isDeleting) {
                    LaunchedEffect(Unit) {
                        // 延迟一小段时间，让用户看到进度指示器
                        delay(300)
                        onDelete()
                        // 删除完成后关闭对话框
                        showDeleteDialog = false
                        isDeleting = false
                        // 显示删除成功提示
                        Toast.makeText(context, context.getString(R.string.delete_success), Toast.LENGTH_SHORT).show()
                    }
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDeleteDialog = false },
                    enabled = !isDeleting
                ) {
                    Text(stringResource(R.string.cancel))
                }
            }
        )
    }

    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 报表标题和日期
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { expanded = !expanded },
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = report.title,
                        style = MaterialTheme.typography.titleLarge
                    )

                    Text(
                        text = report.date,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                // 展开/折叠图标
                Icon(
                    imageVector = if (expanded) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown,
                    contentDescription = if (expanded) "折叠" else "展开",
                    modifier = Modifier.padding(8.dp)
                )
            }

            // 展开/折叠详情
            if (expanded) {
                Spacer(modifier = Modifier.height(16.dp))
                Divider()
                Spacer(modifier = Modifier.height(16.dp))

                // 显示报表详情
                // 显示报表详情
                Text(
                    text = report.content,
                    style = MaterialTheme.typography.bodyMedium
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 操作按钮 - 使用垂直布局排列按钮
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 第一行：编辑按钮
                    Button(
                        onClick = {
                            // 进入编辑模式
                            Toast.makeText(context, "编辑功能即将推出", Toast.LENGTH_SHORT).show()
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Icon(
                            imageVector = Icons.Default.Edit,
                            contentDescription = stringResource(R.string.edit)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(stringResource(R.string.edit))
                    }

                    // 第二行：查看图片按钮
                    Button(
                        onClick = {
                            try {
                                // 查看图片，但不中断音乐播放
                                val file = File(report.imagePath)
                                if (file.exists()) {
                                    // 使用系统图片查看器打开图片
                                    val intent = android.content.Intent(android.content.Intent.ACTION_VIEW)
                                    val uri = androidx.core.content.FileProvider.getUriForFile(
                                        context,
                                        "${context.packageName}.provider",
                                        file
                                    )
                                    intent.setDataAndType(uri, "image/*")
                                    intent.addFlags(android.content.Intent.FLAG_GRANT_READ_URI_PERMISSION)
                                    // 添加FLAG_ACTIVITY_NEW_TASK标志，确保在新任务中启动，不影响当前应用
                                    intent.addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK)

                                    // 检查是否有应用可以处理此Intent
                                    if (intent.resolveActivity(context.packageManager) != null) {
                                        // 设置外部活动状态为true，防止音乐被中断
                                        musicPlayerService?.setExternalActivityRunning(true)
                                        context.startActivity(intent)
                                    } else {
                                        Toast.makeText(context, "没有找到可以查看图片的应用", Toast.LENGTH_SHORT).show()
                                    }
                                } else {
                                    Toast.makeText(context, context.getString(R.string.image_not_found), Toast.LENGTH_SHORT).show()
                                }
                            } catch (e: Exception) {
                                e.printStackTrace()
                                Toast.makeText(context, "打开图片时出错: ${e.message}", Toast.LENGTH_SHORT).show()
                            }
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Icon(
                            imageVector = Icons.Default.Info,
                            contentDescription = stringResource(R.string.view_image)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(stringResource(R.string.view_image))
                    }

                    // 第三行：分享按钮
                    Button(
                        onClick = {
                            // 分享图片，但不中断音乐播放
                            shareImage(report.imagePath, context, musicPlayerService)
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Icon(
                            imageVector = Icons.Default.Share,
                            contentDescription = stringResource(R.string.share_to_wechat)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(stringResource(R.string.share_to_wechat))
                    }

                    // 第四行：删除按钮
                    Button(
                        onClick = {
                            // 显示确认删除对话框
                            showDeleteDialog = true
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.errorContainer,
                            contentColor = MaterialTheme.colorScheme.error
                        ),
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = stringResource(R.string.delete)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(stringResource(R.string.delete))
                    }
                }
            } else {
                // 折叠状态下的操作按钮
                Spacer(modifier = Modifier.height(16.dp))

                // 使用垂直布局排列按钮
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 第一行：查看图片按钮
                    Button(
                        onClick = {
                            try {
                                // 查看图片，但不中断音乐播放
                                val file = File(report.imagePath)
                                if (file.exists()) {
                                    // 使用系统图片查看器打开图片
                                    val intent = android.content.Intent(android.content.Intent.ACTION_VIEW)
                                    val uri = androidx.core.content.FileProvider.getUriForFile(
                                        context,
                                        "${context.packageName}.provider",
                                        file
                                    )
                                    intent.setDataAndType(uri, "image/*")
                                    intent.addFlags(android.content.Intent.FLAG_GRANT_READ_URI_PERMISSION)
                                    // 添加FLAG_ACTIVITY_NEW_TASK标志，确保在新任务中启动，不影响当前应用
                                    intent.addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK)

                                    // 检查是否有应用可以处理此Intent
                                    if (intent.resolveActivity(context.packageManager) != null) {
                                        // 设置外部活动状态为true，防止音乐被中断
                                        musicPlayerService?.setExternalActivityRunning(true)
                                        context.startActivity(intent)
                                    } else {
                                        Toast.makeText(context, "没有找到可以查看图片的应用", Toast.LENGTH_SHORT).show()
                                    }
                                } else {
                                    Toast.makeText(context, context.getString(R.string.image_not_found), Toast.LENGTH_SHORT).show()
                                }
                            } catch (e: Exception) {
                                e.printStackTrace()
                                Toast.makeText(context, "打开图片时出错: ${e.message}", Toast.LENGTH_SHORT).show()
                            }
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Icon(
                            imageVector = Icons.Default.Info,
                            contentDescription = stringResource(R.string.view_image)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(stringResource(R.string.view_image))
                    }

                    // 第二行：分享按钮
                    Button(
                        onClick = {
                            // 分享图片，但不中断音乐播放
                            shareImage(report.imagePath, context, musicPlayerService)
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Icon(
                            imageVector = Icons.Default.Share,
                            contentDescription = stringResource(R.string.share_to_wechat)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(stringResource(R.string.share_to_wechat))
                    }

                    // 第三行：删除按钮
                    Button(
                        onClick = {
                            // 显示确认删除对话框
                            showDeleteDialog = true
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.errorContainer,
                            contentColor = MaterialTheme.colorScheme.error
                        ),
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = stringResource(R.string.delete)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(stringResource(R.string.delete))
                    }
                }
            }
        }
    }
}

/**
 * 分享图片到其他应用
 * @param imagePath 图片路径
 * @param context 上下文
 * @param musicPlayerService 音乐播放服务，用于防止音乐被中断
 */
private fun shareImage(imagePath: String, context: Context, musicPlayerService: MusicPlayerService? = null) {
    try {
        // 检查图片是否存在
        val file = java.io.File(imagePath)
        if (file.exists()) {
            // 创建FileProvider URI
            val contentUri = androidx.core.content.FileProvider.getUriForFile(
                context,
                "${context.packageName}.provider",
                file
            )

            // 创建分享Intent
            val intent = Intent(Intent.ACTION_SEND).apply {
                type = "image/jpeg"
                putExtra(Intent.EXTRA_STREAM, contentUri)
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                // 添加FLAG_ACTIVITY_NEW_TASK标志，确保在新任务中启动，不影响当前应用
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }

            // 创建选择器Intent
            val chooserIntent = Intent.createChooser(intent, context.getString(R.string.share_to_wechat))

            // 确保音乐继续播放，不需要特殊处理

            // 检查是否有应用可以处理分享操作
            if (intent.resolveActivity(context.packageManager) != null) {
                // 设置外部活动状态为true，防止音乐被中断
                musicPlayerService?.setExternalActivityRunning(true)

                // 启动分享选择器
                context.startActivity(chooserIntent)

                // 显示成功提示
                Toast.makeText(context, context.getString(R.string.share_success), Toast.LENGTH_SHORT).show()
            } else {
                // 没有应用可以处理分享
                Toast.makeText(context, context.getString(R.string.share_failed), Toast.LENGTH_SHORT).show()
            }
        } else {
            Toast.makeText(context, context.getString(R.string.image_not_found), Toast.LENGTH_SHORT).show()
        }
    } catch (e: Exception) {
        e.printStackTrace()
        Toast.makeText(context, "分享图片时出错: ${e.message}", Toast.LENGTH_SHORT).show()
    }
}