package com.wanzijiejie.app.ui.components

import android.app.AlertDialog
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.Button
import android.widget.ListView
import android.widget.Spinner
import android.widget.TextView
import android.widget.Toast
import com.wanzijiejie.app.R
import com.wanzijiejie.app.ui.screens.Area

/**
 * 批量分类对话框
 * 用于批量分类未分类的商品
 *
 * 注意：这个对话框会一直显示，直到所有未分类商品都被分类完毕，或者用户点击取消按钮
 */
class BatchClassifyDialog2(
    private val context: Context,
    private val areas: List<Area>,
    private val unclassifiedItems: List<Pair<String, String>>,
    private val onItemClassified: (String, Int) -> Unit,
    private val onAllClassified: () -> Unit
) {
    // 对话框
    private lateinit var dialog: AlertDialog

    // 剩余未分类商品
    private val remainingItems = unclassifiedItems.map { it.first }.toMutableList()

    // 商品列表适配器
    private lateinit var itemsAdapter: ArrayAdapter<String>

    // 商品列表视图
    private lateinit var itemsListView: ListView

    // 区域选择器
    private lateinit var areaSpinner: Spinner

    // 确认按钮
    private lateinit var confirmButton: Button

    // 取消按钮
    private lateinit var cancelButton: Button

    init {
        // 创建对话框
        createDialog()
    }

    /**
     * 创建对话框
     */
    private fun createDialog() {
        // 创建对话框构建器
        val builder = AlertDialog.Builder(context)

        // 设置对话框标题
        builder.setTitle(context.getString(R.string.unclassified_items_title))

        // 设置对话框不可取消
        builder.setCancelable(false)

        // 设置对话框不会被外部点击关闭
        // 注意：setCanceledOnTouchOutside方法只在Dialog类中可用，不在AlertDialog.Builder类中可用

        // 创建对话框视图
        val view = LayoutInflater.from(context).inflate(R.layout.dialog_batch_classify, null)

        // 获取商品列表视图
        itemsListView = view.findViewById(R.id.itemsListView)

        // 获取区域选择器
        areaSpinner = view.findViewById(R.id.areaSpinner)

        // 获取确认按钮
        confirmButton = view.findViewById(R.id.confirmButton)

        // 获取取消按钮
        cancelButton = view.findViewById(R.id.cancelButton)

        // 创建商品列表适配器
        itemsAdapter = ArrayAdapter(context, android.R.layout.simple_list_item_multiple_choice, remainingItems)

        // 设置商品列表适配器
        itemsListView.adapter = itemsAdapter

        // 设置商品列表可多选
        itemsListView.choiceMode = ListView.CHOICE_MODE_MULTIPLE

        // 创建区域列表适配器
        val areaAdapter = ArrayAdapter(context, android.R.layout.simple_spinner_item, areas.map { it.name })

        // 设置区域列表下拉样式
        areaAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)

        // 设置区域列表适配器
        areaSpinner.adapter = areaAdapter

        // 设置区域列表默认选择"其他"
        val otherIndex = areas.indexOfFirst { it.name == "其他" }
        if (otherIndex >= 0) {
            areaSpinner.setSelection(otherIndex)
        }

        // 设置确认按钮点击事件
        confirmButton.setOnClickListener {
            val selectedAreaIndex = areaSpinner.selectedItemPosition

            // 获取选中的商品
            val selectedItems = mutableListOf<String>()
            for (i in 0 until itemsListView.count) {
                if (itemsListView.isItemChecked(i)) {
                    selectedItems.add(remainingItems[i])
                }
            }

            // 如果没有选中商品，则选中当前显示的第一个商品
            if (selectedItems.isEmpty() && remainingItems.isNotEmpty()) {
                selectedItems.add(remainingItems[0])
                // 选中第一个商品
                itemsListView.setItemChecked(0, true)
            }

            // 对每个选中的商品进行分类
            for (item in selectedItems) {
                // 打印分类信息，便于调试
                Toast.makeText(context, "将商品 '$item' 添加到 '${areas[selectedAreaIndex].name}' 区域", Toast.LENGTH_SHORT).show()

                // 回调通知分类结果
                onItemClassified(item, selectedAreaIndex)

                // 从剩余列表中移除已分类的商品
                remainingItems.remove(item)
            }

            // 更新商品列表
            itemsAdapter.clear()
            itemsAdapter.addAll(remainingItems)
            itemsAdapter.notifyDataSetChanged()

            // 只有当没有剩余商品时，才关闭对话框
            if (remainingItems.isEmpty()) {
                dialog.dismiss()
                onAllClassified()
            } else {
                // 清除所有选中状态，准备下一轮选择
                for (i in 0 until itemsListView.count) {
                    itemsListView.setItemChecked(i, false)
                }

                // 更新对话框标题，显示剩余未分类商品数量
                dialog.setTitle(context.getString(R.string.unclassified_items_remaining, remainingItems.size))

                // 打印剩余未分类商品信息，便于调试
                Toast.makeText(context, "还有 ${remainingItems.size} 个未分类商品", Toast.LENGTH_SHORT).show()

                // 确保对话框不会关闭
                dialog.show()
            }
        }

        // 设置取消按钮点击事件
        cancelButton.setOnClickListener {
            // 将所有未分类的商品添加到"其他"区域
            if (remainingItems.isNotEmpty()) {
                val otherIndex = areas.indexOfFirst { it.name == "其他" }
                if (otherIndex >= 0) {
                    // 打印分类信息，便于调试
                    Toast.makeText(context, "将剩余 ${remainingItems.size} 个未分类商品添加到 '其他' 区域", Toast.LENGTH_SHORT).show()

                    // 创建副本以避免循环中修改集合
                    val itemsToProcess = remainingItems.toList()
                    for (item in itemsToProcess) {
                        onItemClassified(item, otherIndex)
                        remainingItems.remove(item)
                    }
                }
            }

            // 关闭对话框
            dialog.dismiss()
            onAllClassified()
        }

        // 设置对话框视图
        builder.setView(view)

        // 创建对话框
        dialog = builder.create()



        // 设置对话框不可取消
        dialog.setCancelable(false)
    }

    /**
     * 显示对话框
     */
    fun show() {
        // 如果对话框已经显示，则不需要再次显示
        if (!dialog.isShowing) {
            dialog.show()
        }



        // 设置对话框不可取消
        dialog.setCancelable(false)
    }
}
