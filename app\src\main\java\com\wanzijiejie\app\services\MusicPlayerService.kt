package com.wanzijiejie.app.services

import android.content.Context
import android.content.res.AssetFileDescriptor
import android.media.MediaPlayer
import android.util.Log
import androidx.compose.runtime.mutableStateOf
import java.io.IOException
import kotlin.random.Random

/**
 * 音乐播放服务类，用于管理应用中的背景音乐播放
 */
class MusicPlayerService private constructor(private val context: Context) {
    companion object {
        private const val TAG = "MusicPlayerService"
        private const val MUSIC_ASSETS_PATH = "music" // assets中的音乐文件夹路径
        private const val PREFS_NAME = "MusicPlayerPrefs" // 偏好设置名称
        private const val KEY_MUSIC_ENABLED = "music_enabled" // 音乐启用状态的键

        @Volatile
        private var instance: MusicPlayerService? = null

        /**
         * 获取MusicPlayerService的单例实例
         */
        fun getInstance(context: Context): MusicPlayerService {
            return instance ?: synchronized(this) {
                instance ?: MusicPlayerService(context.applicationContext).also { instance = it }
            }
        }
    }

    // 媒体播放器
    private var mediaPlayer: MediaPlayer? = null

    // 音乐文件列表
    private var musicFiles = mutableListOf<String>()

    // 当前播放的音乐索引
    private var currentMusicIndex = -1

    // 音乐是否启用的状态（控制下次启动时是否播放）
    val isMusicEnabled = mutableStateOf(false)

    // 当前是否正在播放音乐
    private var isCurrentlyPlaying = false

    // 是否正在执行外部活动（查看图片、分享等），防止音乐被中断
    private var isExternalActivityRunning = false

    // 初始化
    init {
        // 从偏好设置中加载音乐启用状态
        loadMusicEnabledState()

        // 加载音乐文件
        loadMusicFiles()
    }

    /**
     * 从偏好设置中加载音乐启用状态
     */
    private fun loadMusicEnabledState() {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        isMusicEnabled.value = prefs.getBoolean(KEY_MUSIC_ENABLED, true) // 默认启用音乐
    }

    /**
     * 保存音乐启用状态到偏好设置
     * 此方法控制下次启动时是否自动播放音乐
     */
    fun saveMusicEnabledState(enabled: Boolean) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit().putBoolean(KEY_MUSIC_ENABLED, enabled).apply()
        isMusicEnabled.value = enabled

        if (enabled) {
            // 如果启用音乐，开始播放
            startMusic()
        } else {
            // 如果禁用音乐，停止播放
            stopMusic()
        }
    }

    /**
     * 加载音乐文件
     */
    private fun loadMusicFiles() {
        try {
            // 从assets目录加载音乐文件
            val assetManager = context.assets

            // 获取music文件夹中的所有文件
            val files = assetManager.list(MUSIC_ASSETS_PATH)

            if (files != null && files.isNotEmpty()) {
                // 过滤出MP3文件
                val mp3Files = files.filter { it.lowercase().endsWith(".mp3") }

                if (mp3Files.isNotEmpty()) {
                    musicFiles.clear()
                    musicFiles.addAll(mp3Files)
                    Log.d(TAG, "Loaded ${musicFiles.size} music files from assets")
                } else {
                    Log.d(TAG, "No MP3 files found in assets/$MUSIC_ASSETS_PATH")
                }
            } else {
                Log.d(TAG, "No files found in assets/$MUSIC_ASSETS_PATH")
            }
        } catch (e: IOException) {
            Log.e(TAG, "Error loading music files from assets", e)
        }
    }

    /**
     * 开始播放音乐
     */
    fun startMusic() {
        // 如果没有启用音乐或没有音乐文件，则不播放
        if (!isMusicEnabled.value || musicFiles.isEmpty()) {
            return
        }

        // 如果已经在播放，则不需要重新开始
        if (isCurrentlyPlaying) {
            return
        }

        try {
            // 如果已经有播放器在播放，先停止
            stopMusic()

            // 随机选择一首音乐
            currentMusicIndex = if (musicFiles.size == 1) {
                0
            } else {
                Random.nextInt(0, musicFiles.size)
            }

            val musicFileName = musicFiles[currentMusicIndex]
            Log.d(TAG, "Playing music: $musicFileName")

            // 创建新的媒体播放器
            mediaPlayer = MediaPlayer().apply {
                // 从assets加载音乐文件
                val afd: AssetFileDescriptor = context.assets.openFd("$MUSIC_ASSETS_PATH/$musicFileName")
                setDataSource(afd.fileDescriptor, afd.startOffset, afd.length)
                afd.close()

                setOnCompletionListener {
                    // 播放完成后，播放下一首
                    isCurrentlyPlaying = false
                    playNextMusic()
                }
                setOnErrorListener { _, what, extra ->
                    Log.e(TAG, "MediaPlayer error: what=$what, extra=$extra")
                    isCurrentlyPlaying = false
                    playNextMusic()
                    true
                }
                prepare()
                start()
                isCurrentlyPlaying = true
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error starting music", e)
            isCurrentlyPlaying = false
            playNextMusic() // 出错时尝试播放下一首
        }
    }

    /**
     * 播放下一首音乐
     */
    private fun playNextMusic() {
        if (!isMusicEnabled.value || musicFiles.isEmpty()) {
            return
        }

        // 如果已经在播放，则不需要重新开始
        if (isCurrentlyPlaying) {
            return
        }

        try {
            // 释放当前播放器
            mediaPlayer?.release()
            mediaPlayer = null

            // 随机选择下一首，确保不重复
            val nextIndex = if (musicFiles.size == 1) {
                0
            } else {
                var newIndex: Int
                do {
                    newIndex = Random.nextInt(0, musicFiles.size)
                } while (newIndex == currentMusicIndex && musicFiles.size > 1)
                newIndex
            }

            currentMusicIndex = nextIndex
            val musicFileName = musicFiles[currentMusicIndex]
            Log.d(TAG, "Playing next music: $musicFileName")

            // 创建新的媒体播放器
            mediaPlayer = MediaPlayer().apply {
                // 从assets加载音乐文件
                val afd: AssetFileDescriptor = context.assets.openFd("$MUSIC_ASSETS_PATH/$musicFileName")
                setDataSource(afd.fileDescriptor, afd.startOffset, afd.length)
                afd.close()

                setOnCompletionListener {
                    // 播放完成后，播放下一首
                    isCurrentlyPlaying = false
                    playNextMusic()
                }
                setOnErrorListener { _, what, extra ->
                    Log.e(TAG, "MediaPlayer error: what=$what, extra=$extra")
                    isCurrentlyPlaying = false
                    playNextMusic()
                    true
                }
                prepare()
                start()
                isCurrentlyPlaying = true
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error playing next music", e)
            isCurrentlyPlaying = false

            // 出错时延迟一秒后尝试下一首
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                playNextMusic()
            }, 1000)
        }
    }

    /**
     * 停止播放音乐
     * @param force 是否强制停止，即使外部活动正在运行
     */
    fun stopMusic(force: Boolean = false) {
        // 如果外部活动正在运行且不是强制停止，则不停止音乐
        if (isExternalActivityRunning && !force) {
            return
        }

        try {
            mediaPlayer?.apply {
                if (isPlaying) {
                    stop()
                }
                release()
            }
            mediaPlayer = null
            isCurrentlyPlaying = false
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping music", e)
        }
    }

    /**
     * 刷新音乐文件列表
     */
    fun refreshMusicFiles() {
        loadMusicFiles()
        if (isMusicEnabled.value && !isCurrentlyPlaying) {
            startMusic()
        }
    }

    /**
     * 设置外部活动状态
     * @param running 是否正在运行外部活动
     */
    fun setExternalActivityRunning(running: Boolean) {
        isExternalActivityRunning = running

        // 如果外部活动结束，并且音乐已启用但没有播放，则开始播放
        if (!running && isMusicEnabled.value && !isCurrentlyPlaying) {
            startMusic()
        }
    }

    /**
     * 获取外部活动状态
     * @return 是否正在运行外部活动
     */
    fun isExternalActivityRunning(): Boolean {
        return isExternalActivityRunning
    }
}
