package com.wanzijiejie.app.ui.navigation

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import com.wanzijiejie.app.services.MusicPlayerService
import com.wanzijiejie.app.ui.screens.CategoryManagementScreen
import com.wanzijiejie.app.ui.screens.HuangJiaShanScreen
import com.wanzijiejie.app.ui.screens.HistoryScreen
import com.wanzijiejie.app.ui.screens.HomeScreen
import com.wanzijiejie.app.ui.screens.ReportScreen
import com.wanzijiejie.app.ui.screens.SplashScreen

/**
 * 应用程序的导航路由
 */
object AppDestinations {
    const val SPLASH_ROUTE = "splash"
    const val HOME_ROUTE = "home"
    const val HUANG_JIA_SHAN_ROUTE = "huangJiaShan"
    const val REPORT_ROUTE = "report/{dateMillis}"
    const val HISTORY_ROUTE = "history"
    const val CATEGORY_MANAGEMENT_ROUTE = "categoryManagement"

    // 生成报表路由
    fun createReportRoute(dateMillis: Long): String {
        return "report/$dateMillis"
    }
}

/**
 * 应用程序的导航图
 */
@Composable
fun AppNavigation(
    navController: NavHostController,
    modifier: Modifier = Modifier,
    musicPlayerService: MusicPlayerService
) {
    NavHost(
        navController = navController,
        startDestination = AppDestinations.SPLASH_ROUTE,
        modifier = modifier
    ) {
        // 启动屏幕
        composable(AppDestinations.SPLASH_ROUTE) {
            SplashScreen(
                onSplashFinished = {
                    navController.navigate(AppDestinations.HUANG_JIA_SHAN_ROUTE) {
                        // 清除回退栈，使用户不能返回到启动屏幕
                        popUpTo(AppDestinations.SPLASH_ROUTE) { inclusive = true }
                    }
                }
            )
        }

        composable(AppDestinations.HOME_ROUTE) {
            HomeScreen(
                onNavigateToHuangJiaShan = {
                    navController.navigate(AppDestinations.HUANG_JIA_SHAN_ROUTE)
                }
            )
        }

        composable(AppDestinations.HUANG_JIA_SHAN_ROUTE) {
            HuangJiaShanScreen(
                navController = navController,
                musicPlayerService = musicPlayerService
            )
        }

        composable(
            route = AppDestinations.REPORT_ROUTE,
            arguments = listOf(
                navArgument("dateMillis") {
                    type = NavType.LongType
                }
            )
        ) { backStackEntry ->
            val dateMillis = backStackEntry.arguments?.getLong("dateMillis") ?: System.currentTimeMillis()
            ReportScreen(
                dateMillis = dateMillis,
                navController = navController,
                musicPlayerService = musicPlayerService
            )
        }

        composable(AppDestinations.HISTORY_ROUTE) {
            HistoryScreen(
                navController = navController,
                musicPlayerService = musicPlayerService
            )
        }

        composable(AppDestinations.CATEGORY_MANAGEMENT_ROUTE) {
            CategoryManagementScreen(
                navController = navController,
                musicPlayerService = musicPlayerService
            )
        }
    }
}
