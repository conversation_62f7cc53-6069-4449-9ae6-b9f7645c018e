package com.wanzijiejie.app.model

/**
 * 商品数据模型
 */
data class Product(
    val id: String = java.util.UUID.randomUUID().toString(),
    var name: String = "",
    var quantity: String = ""
)

/**
 * 区域数据模型
 */
data class Area(
    val id: String,
    val name: String,
    val sales: String,
    val inventory: String,
    val remarks: String,
    val products: MutableList<Product> = mutableListOf()
)
