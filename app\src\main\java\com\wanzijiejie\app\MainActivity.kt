package com.wanzijiejie.app

import android.Manifest
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.core.content.ContextCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.navigation.compose.rememberNavController
import com.wanzijiejie.app.services.MusicPlayerService
import com.wanzijiejie.app.ui.navigation.AppNavigation
import com.wanzijiejie.app.ui.theme.丸子姐姐报表Theme

class MainActivity : ComponentActivity() {
    // 音乐播放服务
    private lateinit var musicPlayerService: MusicPlayerService

    // 权限请求回调
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.entries.all { it.value }
        if (allGranted) {
            // 权限已授予，刷新音乐文件并开始播放
            musicPlayerService.refreshMusicFiles()
        } else {
            // 权限被拒绝
            Toast.makeText(this, getString(R.string.permission_required_music), Toast.LENGTH_SHORT).show()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 初始化音乐播放服务
        musicPlayerService = MusicPlayerService.getInstance(this)

        // 检查并请求权限
        checkAndRequestPermissions()

        enableEdgeToEdge()
        setContent {
            丸子姐姐报表Theme {
                MainApp(musicPlayerService)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        // 设置外部活动状态为false
        musicPlayerService.setExternalActivityRunning(false)

        // 如果音乐已启用，开始播放
        if (musicPlayerService.isMusicEnabled.value) {
            musicPlayerService.startMusic()
        }
    }

    override fun onPause() {
        super.onPause()
        // 停止音乐播放，但不改变启用状态
        // 这样下次启动时，如果启用状态为true，仍会自动播放
        // 注意：如果外部活动正在运行，则不会停止音乐
        musicPlayerService.stopMusic()
    }

    /**
     * 检查并请求必要的权限
     * 注意：我们不再请求音频权限，而是直接从assets加载音乐文件
     */
    private fun checkAndRequestPermissions() {
        // 直接从assets加载音乐文件，不请求权限
        musicPlayerService.refreshMusicFiles()
    }
}

@Composable
fun MainApp(musicPlayerService: MusicPlayerService) {
    val navController = rememberNavController()
    val lifecycleOwner = LocalLifecycleOwner.current

    // 监听生命周期事件
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_RESUME -> {
                    // 如果音乐已启用，开始播放
                    if (musicPlayerService.isMusicEnabled.value) {
                        musicPlayerService.startMusic()
                    }
                }
                Lifecycle.Event.ON_PAUSE -> {
                    // 停止音乐播放
                    musicPlayerService.stopMusic()
                }
                else -> {}
            }
        }

        // 添加观察者
        lifecycleOwner.lifecycle.addObserver(observer)

        // 当 Composable 被处理时移除观察者
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }

    Surface(
        modifier = Modifier.fillMaxSize(),
        color = MaterialTheme.colorScheme.background
    ) {
        Scaffold { innerPadding ->
            AppNavigation(
                navController = navController,
                modifier = Modifier.padding(innerPadding),
                musicPlayerService = musicPlayerService
            )
        }
    }
}