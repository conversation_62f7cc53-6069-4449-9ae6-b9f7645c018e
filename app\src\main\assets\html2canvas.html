<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML2Canvas Screenshot</title>
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        #container {
            padding: 20px;
            background-color: #fff;
            width: 100%;
            box-sizing: border-box;
        }
        h2, h3 {
            text-align: center;
            margin-bottom: 15px;
            background-color: #fff; /* 确保背景是白色 */
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
            width: 25%;
        }
        th {
            background-color: #fff; /* 修改为白色背景 */
        }
    </style>
</head>
<body>
    <div id="container"></div>
    <script>
        // 接收来自Android的数据并生成表格
        function generateReport(reportData) {
            const data = JSON.parse(reportData);
            const container = document.getElementById('container');

            // 添加标题
            const title = document.createElement('h2');
            title.textContent = `${data.date} - ${data.title}`;
            container.appendChild(title);

            // 添加各区域内容
            data.areas.forEach(area => {
                const sectionTitle = document.createElement('h3');
                sectionTitle.textContent = area.name;
                sectionTitle.style.backgroundColor = '#fff'; // 确保背景是白色
                container.appendChild(sectionTitle);

                const table = document.createElement('table');

                // 添加表头
                const thead = document.createElement('thead');
                const headerRow = document.createElement('tr');
                ['商品名', '数量', '商品名', '数量'].forEach(text => {
                    const th = document.createElement('th');
                    th.textContent = text;
                    th.style.backgroundColor = '#fff'; // 确保背景是白色
                    headerRow.appendChild(th);
                });
                thead.appendChild(headerRow);
                table.appendChild(thead);

                // 添加表格内容
                const tbody = document.createElement('tbody');
                area.products.forEach(product => {
                    const row = document.createElement('tr');
                    [product.name1, product.quantity1, product.name2, product.quantity2].forEach(text => {
                        const td = document.createElement('td');
                        td.textContent = text || '';
                        td.style.backgroundColor = '#fff'; // 确保背景是白色
                        row.appendChild(td);
                    });
                    tbody.appendChild(row);
                });
                table.appendChild(tbody);

                container.appendChild(table);
            });

            // 截图并返回给Android
            setTimeout(() => {
                html2canvas(container, { scale: 2 }).then(canvas => {
                    const imgData = canvas.toDataURL('image/png');
                    // 调用Android提供的接口
                    Android.processScreenshot(imgData);
                });
            }, 500);
        }
    </script>
</body>
</html>
