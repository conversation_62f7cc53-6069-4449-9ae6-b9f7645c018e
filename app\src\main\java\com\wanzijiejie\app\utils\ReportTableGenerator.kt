package com.wanzijiejie.app.utils

import android.content.ContentValues
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Typeface
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.view.View
import android.widget.LinearLayout
import android.widget.TableLayout
import android.widget.TableRow
import android.widget.TextView
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import com.wanzijiejie.app.R
import com.wanzijiejie.app.ui.screens.Area
import java.io.File
import java.io.FileOutputStream
import java.io.OutputStream
import java.util.UUID

/**
 * 报表表格生成器
 */
class ReportTableGenerator(private val context: Context) {

    // 获取统一的字体，确保在所有设备上使用相同的字体
    private fun getUnifiedTypeface(): Typeface {
        // 使用黑体字体，与图片中的字体相似
        return try {
            // 尝试使用系统内置的黑体字体
            Typeface.create("sans-serif-black", Typeface.BOLD)
        } catch (e: Exception) {
            try {
                // 如果没有sans-serif-black，尝试使用黑体
                Typeface.create("黑体", Typeface.BOLD)
            } catch (e: Exception) {
                // 如果都失败，使用默认粗体字体
                Typeface.DEFAULT_BOLD
            }
        }
    }

    // 获取统一的粗体字体（与getUnifiedTypeface相同，都使用粗体）
    private fun getUnifiedBoldTypeface(): Typeface {
        return getUnifiedTypeface() // 直接使用相同的粗体字体
    }

    interface ScreenshotCallback {
        fun onScreenshotTaken(success: Boolean, bitmap: Bitmap? = null)
    }

    /**
     * 生成报表表格视图
     */
    fun generateReportTableView(date: String, title: String, areas: List<Area>): View {
        // 创建根布局 - 使用更大的宽度，类似电脑网页
        val rootLayout = LinearLayout(context).apply {
            orientation = LinearLayout.VERTICAL
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
            // 使用固定的内边距，而不是基于屏幕宽度的百分比
            setPadding(60, 60, 60, 60) // 使用更大的内边距，增强可读性
            setBackgroundColor(Color.WHITE)
        }

        // 添加标题容器
        val titleContainer = LinearLayout(context).apply {
            orientation = LinearLayout.VERTICAL
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
            gravity = android.view.Gravity.CENTER
        }

        // 添加日期标题
        val dateTextView = TextView(context).apply {
            text = date
            textSize = 24f // 使用更大的字体大小
            typeface = getUnifiedBoldTypeface() // 使用统一的粗体字体
            setTextColor(Color.BLACK)
            textAlignment = View.TEXT_ALIGNMENT_CENTER
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                bottomMargin = 8 // 增加间距
            }
        }
        titleContainer.addView(dateTextView)

        // 添加页面名称标题
        val titleTextView = TextView(context).apply {
            text = title
            textSize = 28f // 使用更大的字体大小
            typeface = getUnifiedBoldTypeface() // 使用统一的粗体字体
            setTextColor(Color.BLACK)
            textAlignment = View.TEXT_ALIGNMENT_CENTER
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
        }
        titleContainer.addView(titleTextView)

        // 添加标题容器到根布局
        val titleContainerParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        ).apply {
            bottomMargin = 25
        }
        rootLayout.addView(titleContainer, titleContainerParams)

        // 为每个区域创建独立的表格，不再使用一个大表格

        // 为每个区域创建表格行
        areas.forEach { area ->
            // 添加区域标题（作为独立的文本视图，不是表格的一部分）
            val sectionTitle = TextView(context).apply {
                text = area.name
                textSize = 20f // 适当的字体大小
                typeface = getUnifiedBoldTypeface() // 使用统一的粗体字体
                setTextColor(Color.BLACK)
                textAlignment = View.TEXT_ALIGNMENT_CENTER
                // 不设置内边距，保持简洁
                layoutParams = LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT
                ).apply {
                    topMargin = 30 // 增加上边距，与上方内容分开
                    bottomMargin = 10 // 与下方表格保持适当距离
                }
                // 确保没有背景色或边框
                background = null
            }
            rootLayout.addView(sectionTitle)

            // 为当前区域创建独立的表格
            val areaTableLayout = TableLayout(context).apply {
                layoutParams = LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT
                )
                setBackgroundColor(Color.BLACK) // 表格边框颜色
                setPadding(1, 1, 1, 1) // 表格边框宽度
                isStretchAllColumns = false // 不平均分配列宽，而是根据内容自适应
                // 设置固定列宽，类似于 tableLayout: fixed
                isShrinkAllColumns = false
            }

            // 移除表头行，直接显示数据内容

            // 添加数据行
            if (area.products.isEmpty()) {
                // 如果没有商品，添加一行空白行
                val emptyRow = TableRow(context).apply {
                    layoutParams = TableLayout.LayoutParams(
                        TableLayout.LayoutParams.MATCH_PARENT,
                        TableLayout.LayoutParams.WRAP_CONTENT
                    )
                    setBackgroundColor(Color.WHITE)
                }

                val emptyCell = TextView(context).apply {
                    text = "没有要报的"
                    textSize = 18f // 使用更大的字体大小
                    typeface = getUnifiedBoldTypeface() // 使用统一的粗体字体
                    setTextColor(Color.GRAY)
                    textAlignment = View.TEXT_ALIGNMENT_CENTER
                    setPadding(16, 16, 16, 16) // 使用更大的内边距
                    // 添加单元格边框
                    background = android.graphics.drawable.GradientDrawable().apply {
                        setStroke(2, Color.BLACK) // 使用更粗的边框
                        setColor(Color.WHITE)
                    }
                }
                emptyRow.addView(emptyCell, TableRow.LayoutParams(TableRow.LayoutParams.MATCH_PARENT, TableRow.LayoutParams.WRAP_CONTENT, 4f))
                areaTableLayout.addView(emptyRow)
            } else {
                area.products.forEach { product ->
                    val dataRow = TableRow(context).apply {
                        layoutParams = TableLayout.LayoutParams(
                            TableLayout.LayoutParams.MATCH_PARENT,
                            TableLayout.LayoutParams.WRAP_CONTENT
                        )
                        setBackgroundColor(Color.WHITE)
                    }

                    // 数据单元格
                    val cellData = listOf(product.name1, product.quantity1, product.name2, product.quantity2)
                    cellData.forEachIndexed { index, cellText ->
                        val cell = TextView(context).apply {
                            text = cellText
                            textSize = 18f // 使用更大的字体大小
                            typeface = getUnifiedBoldTypeface() // 使用统一的粗体字体
                            setTextColor(Color.BLACK)
                            textAlignment = View.TEXT_ALIGNMENT_CENTER
                            setPadding(16, 16, 16, 16) // 使用更大的内边距
                            // 商品名和数量都不换行显示
                            maxLines = 1 // 单行显示
                            // 不使用省略号，而是确保单元格足够宽
                            // 添加单元格边框
                            background = android.graphics.drawable.GradientDrawable().apply {
                                setStroke(2, Color.BLACK) // 使用更粗的边框
                                setColor(Color.WHITE)
                            }
                        }
                        // 商品名列宽度大于数量列
                        val weight = if (index % 2 == 0) 1.5f else 0.5f
                        val params = TableRow.LayoutParams(0, TableRow.LayoutParams.WRAP_CONTENT, weight)
                        // 设置单元格高度通过内边距来实现
                        cell.setPadding(8, 16, 8, 16) // 增加上下内边距来增加高度
                        dataRow.addView(cell, params)
                    }
                    areaTableLayout.addView(dataRow)
                }
            }

            // 将表格添加到根布局
            rootLayout.addView(areaTableLayout)

            // 添加区域之间的间距
            val spacer = View(context).apply {
                layoutParams = LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    20 // 区域之间的间距
                )
                setBackgroundColor(Color.WHITE)
            }
            rootLayout.addView(spacer)
        }

        return rootLayout
    }

    /**
     * 截取视图并保存为图片
     */
    fun captureViewAndSave(view: View, callback: ScreenshotCallback) {
        // 使用异步处理，避免主线程阻塞
        Thread {
            try {
                // 使用更大的宽度，类似电脑网页
                // 我们使用一个大屏幕的宽度，而不是手机屏幕宽度
                val largeWidth = 2000 // 使用更大的固定宽度，类似电脑屏幕

                // 测量视图大小
                val widthMeasureSpec = View.MeasureSpec.makeMeasureSpec(largeWidth, View.MeasureSpec.EXACTLY)
                val heightMeasureSpec = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
                view.measure(widthMeasureSpec, heightMeasureSpec)

                // 设置布局
                val width = view.measuredWidth
                val height = view.measuredHeight
                view.layout(0, 0, width, height)

                // 创建位图 - 降低缩放比例以减少内存使用
                val scale = 2.0f // 降低缩放比例，减少内存使用但仍保持清晰度
                val scaledWidth = (width * scale).toInt()
                val scaledHeight = (height * scale).toInt()

                // 使用RGB_565配置减少内存使用（每像素16位而不是32位）
                val bitmap = Bitmap.createBitmap(scaledWidth, scaledHeight, Bitmap.Config.RGB_565)
                val canvas = Canvas(bitmap)
                canvas.scale(scale, scale) // 缩放画布
                canvas.drawColor(Color.WHITE) // 设置背景色

                // 在主线程上绘制视图
                android.os.Handler(android.os.Looper.getMainLooper()).post {
                    view.draw(canvas)

                    // 在绘制完成后继续处理
                    Thread {
                        try {
                            // 保存位图
                            val success = saveBitmapToGallery(bitmap)

                            // 在主线程上回调结果
                            android.os.Handler(android.os.Looper.getMainLooper()).post {
                                callback.onScreenshotTaken(success, bitmap)
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                            // 在主线程上回调失败结果
                            android.os.Handler(android.os.Looper.getMainLooper()).post {
                                callback.onScreenshotTaken(false, null)
                            }
                        }
                    }.start()
                }
            } catch (e: Exception) {
                e.printStackTrace()
                // 在主线程上回调失败结果
                android.os.Handler(android.os.Looper.getMainLooper()).post {
                    callback.onScreenshotTaken(false, null)
                }
            }
        }.start()
    }

    /**
     * 保存位图到相册
     * 使用JPEG格式和较低的质量设置来减少文件大小和内存使用
     */
    private fun saveBitmapToGallery(bitmap: Bitmap): Boolean {
        val filename = "screenshot_report_${UUID.randomUUID()}.jpg"

        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                val contentValues = ContentValues().apply {
                    put(MediaStore.Images.Media.DISPLAY_NAME, filename)
                    put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg")
                    put(MediaStore.Images.Media.RELATIVE_PATH, Environment.DIRECTORY_PICTURES)
                    put(MediaStore.Images.Media.IS_PENDING, 1)
                }

                val uri = context.contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)

                uri?.let {
                    context.contentResolver.openOutputStream(it)?.use { outputStream ->
                        // 使用JPEG格式和85%的质量，这通常是质量和文件大小的良好平衡点
                        bitmap.compress(Bitmap.CompressFormat.JPEG, 85, outputStream)

                        // 确保输出流被刷新和关闭
                        outputStream.flush()
                    }

                    contentValues.clear()
                    contentValues.put(MediaStore.Images.Media.IS_PENDING, 0)
                    context.contentResolver.update(it, contentValues, null, null)

                    // 手动触发垃圾回收，帮助释放内存
                    System.gc()

                    true
                } ?: false
            } else {
                val imagesDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)
                val image = File(imagesDir, filename)

                FileOutputStream(image).use { outputStream ->
                    // 使用JPEG格式和85%的质量
                    bitmap.compress(Bitmap.CompressFormat.JPEG, 85, outputStream)

                    // 确保输出流被刷新
                    outputStream.flush()
                }

                // 通知相册更新
                val values = ContentValues().apply {
                    put(MediaStore.Images.Media.DATA, image.absolutePath)
                    put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg")
                }
                context.contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values)

                // 手动触发垃圾回收，帮助释放内存
                System.gc()

                true
            }
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }
}
