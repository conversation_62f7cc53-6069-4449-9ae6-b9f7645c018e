package com.wanzijiejie.app.utils

import android.content.ContentValues
import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.util.Base64
import android.webkit.JavascriptInterface
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.Toast
import androidx.webkit.WebSettingsCompat
import androidx.webkit.WebViewFeature
import org.json.JSONArray
import org.json.JSONObject
import java.io.File
import java.io.FileOutputStream
import java.io.OutputStream
import java.util.UUID
import com.wanzijiejie.app.ui.screens.Area
import com.wanzijiejie.app.ui.screens.Product

class ScreenshotUtil(private val context: Context) {
    
    interface ScreenshotCallback {
        fun onScreenshotTaken(success: <PERSON>olean)
    }
    
    // JavaScript接口
    inner class WebAppInterface(private val callback: ScreenshotCallback) {
        @JavascriptInterface
        fun processScreenshot(base64Image: String) {
            // 移除Base64前缀
            val base64Data = base64Image.split(",")[1]
            
            // 转换为Bitmap
            val decodedBytes = Base64.decode(base64Data, Base64.DEFAULT)
            val bitmap = android.graphics.BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.size)
            
            // 保存到相册
            val success = saveBitmapToGallery(bitmap)
            callback.onScreenshotTaken(success)
        }
        
        private fun saveBitmapToGallery(bitmap: Bitmap): Boolean {
            val filename = "screenshot_report_${UUID.randomUUID()}.png"
            
            return try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    val contentValues = ContentValues().apply {
                        put(MediaStore.Images.Media.DISPLAY_NAME, filename)
                        put(MediaStore.Images.Media.MIME_TYPE, "image/png")
                        put(MediaStore.Images.Media.RELATIVE_PATH, Environment.DIRECTORY_PICTURES)
                        put(MediaStore.Images.Media.IS_PENDING, 1)
                    }
                    
                    val uri = context.contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)
                    
                    uri?.let {
                        context.contentResolver.openOutputStream(it)?.use { outputStream ->
                            bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream)
                        }
                        
                        contentValues.clear()
                        contentValues.put(MediaStore.Images.Media.IS_PENDING, 0)
                        context.contentResolver.update(it, contentValues, null, null)
                        true
                    } ?: false
                } else {
                    val imagesDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)
                    val image = File(imagesDir, filename)
                    
                    FileOutputStream(image).use { outputStream ->
                        bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream)
                    }
                    
                    // 通知相册更新
                    val values = ContentValues().apply {
                        put(MediaStore.Images.Media.DATA, image.absolutePath)
                        put(MediaStore.Images.Media.MIME_TYPE, "image/png")
                    }
                    context.contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values)
                    true
                }
            } catch (e: Exception) {
                e.printStackTrace()
                false
            }
        }
    }
    
    fun takeScreenshot(date: String, title: String, areas: List<Area>, callback: ScreenshotCallback) {
        val webView = WebView(context)
        
        // 配置WebView
        webView.settings.javaScriptEnabled = true
        webView.settings.domStorageEnabled = true
        
        // 添加JavaScript接口
        webView.addJavascriptInterface(WebAppInterface(callback), "Android")
        
        // 加载HTML
        webView.webViewClient = object : WebViewClient() {
            override fun onPageFinished(view: WebView, url: String) {
                // 将数据转换为JSON
                val jsonData = createJsonData(date, title, areas)
                
                // 调用JavaScript函数生成报表
                webView.evaluateJavascript("generateReport('$jsonData')", null)
            }
        }
        
        // 加载HTML文件
        webView.loadUrl("file:///android_asset/html2canvas.html")
    }
    
    private fun createJsonData(date: String, title: String, areas: List<Area>): String {
        val jsonObject = JSONObject()
        jsonObject.put("date", date)
        jsonObject.put("title", title)
        
        val areasArray = JSONArray()
        areas.forEach { area ->
            val areaObject = JSONObject()
            areaObject.put("name", area.name)
            
            val productsArray = JSONArray()
            area.products.forEach { product ->
                val productObject = JSONObject()
                productObject.put("name1", product.name1)
                productObject.put("quantity1", product.quantity1)
                productObject.put("name2", product.name2)
                productObject.put("quantity2", product.quantity2)
                productsArray.put(productObject)
            }
            
            areaObject.put("products", productsArray)
            areasArray.put(areaObject)
        }
        
        jsonObject.put("areas", areasArray)
        return jsonObject.toString().replace("'", "\\'") // 转义单引号，防止JavaScript解析错误
    }
}
