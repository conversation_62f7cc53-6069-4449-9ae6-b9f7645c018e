<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/unclassified_items_title"
        android:textStyle="bold"
        android:textSize="16sp"
        android:layout_marginBottom="8dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/select_area_for_items"
        android:textStyle="bold"
        android:textSize="16sp"
        android:layout_marginBottom="8dp" />

    <Spinner
        android:id="@+id/areaSpinner"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/select_items_to_classify"
        android:textStyle="bold"
        android:textSize="16sp"
        android:layout_marginBottom="8dp" />

    <ListView
        android:id="@+id/itemsListView"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:layout_marginBottom="16dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/confirmButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/confirm" />

        <Space
            android:layout_width="8dp"
            android:layout_height="wrap_content" />

        <Button
            android:id="@+id/cancelButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/cancel" />

    </LinearLayout>

</LinearLayout>
