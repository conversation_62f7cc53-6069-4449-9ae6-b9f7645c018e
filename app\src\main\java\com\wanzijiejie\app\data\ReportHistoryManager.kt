package com.wanzijiejie.app.data

import android.content.Context
import android.graphics.Bitmap
import android.os.Environment
import androidx.compose.runtime.mutableStateListOf
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wanzijiejie.app.ui.screens.Area
import com.wanzijiejie.app.ui.screens.ReportHistory
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.UUID

/**
 * 报表历史记录管理器
 */
class ReportHistoryManager(private val context: Context) {
    private val sharedPreferences = context.getSharedPreferences("report_history", Context.MODE_PRIVATE)
    private val gson = Gson()

    /**
     * 保存报表历史记录
     */
    fun saveReport(date: String, title: String, areas: List<Area>, bitmap: Bitmap): ReportHistory {
        // 生成唯一ID
        val id = UUID.randomUUID().toString()

        // 保存图片
        val imagePath = saveImageToStorage(bitmap, id)

        // 生成报表内容
        val content = generateReportContent(areas)

        // 创建报表历史记录
        val report = ReportHistory(
            id = id,
            date = date,
            title = title,
            content = content,
            imagePath = imagePath,
            timestamp = System.currentTimeMillis(),
            areas = areas.map { it.copy(products = it.products.toMutableList()) }.toMutableList()
        )

        // 获取现有的历史记录
        val reports = loadReports()

        // 添加新的历史记录
        reports.add(0, report) // 添加到列表开头

        // 保存历史记录
        saveReports(reports)

        return report
    }

    /**
     * 更新报表历史记录
     */
    fun updateReport(report: ReportHistory) {
        // 获取现有的历史记录
        val reports = loadReports()

        // 查找要更新的报表
        val index = reports.indexOfFirst { it.id == report.id }
        if (index >= 0) {
            // 更新报表内容
            val updatedContent = generateReportContent(report.areas)
            val updatedReport = report.copy(content = updatedContent)

            // 替换报表
            reports[index] = updatedReport

            // 保存历史记录
            saveReports(reports)
        }
    }

    /**
     * 加载报表历史记录
     */
    fun loadReports(): MutableList<ReportHistory> {
        val reportsJson = sharedPreferences.getString("reports", null) ?: return mutableStateListOf()
        val type = object : TypeToken<List<ReportHistory>>() {}.type
        val reports = gson.fromJson<List<ReportHistory>>(reportsJson, type)
        return mutableStateListOf(*reports.toTypedArray())
    }

    /**
     * 删除报表历史记录
     */
    fun deleteReport(id: String) {
        // 获取现有的历史记录
        val reports = loadReports()

        // 查找要删除的报表
        val report = reports.find { it.id == id } ?: return

        // 删除图片文件
        val imageFile = File(report.imagePath)
        if (imageFile.exists()) {
            imageFile.delete()
        }

        // 从列表中删除报表
        reports.removeIf { it.id == id }

        // 保存历史记录
        saveReports(reports)
    }

    /**
     * 保存历史记录列表
     */
    private fun saveReports(reports: List<ReportHistory>) {
        val reportsJson = gson.toJson(reports)
        sharedPreferences.edit().putString("reports", reportsJson).apply()
    }

    /**
     * 保存图片到存储
     * 使用较低的JPEG质量以减少文件大小和内存使用
     */
    private fun saveImageToStorage(bitmap: Bitmap, id: String): String {
        // 创建目录
        val directory = File(context.getExternalFilesDir(Environment.DIRECTORY_PICTURES), "reports")
        if (!directory.exists()) {
            directory.mkdirs()
        }

        // 创建文件
        val file = File(directory, "$id.jpg")

        try {
            // 使用try-with-resources确保资源正确关闭
            FileOutputStream(file).use { outputStream ->
                // 使用85%的JPEG质量，这通常是质量和文件大小的良好平衡点
                bitmap.compress(Bitmap.CompressFormat.JPEG, 85, outputStream)
                outputStream.flush()
            }

            // 手动触发垃圾回收，帮助释放内存
            System.gc()
        } catch (e: Exception) {
            e.printStackTrace()
        }

        return file.absolutePath
    }

    /**
     * 生成报表内容
     */
    private fun generateReportContent(areas: List<Area>): String {
        val content = StringBuilder()

        areas.forEach { area ->
            content.append("【${area.name}】\n")

            if (area.products.isEmpty()) {
                content.append("无商品\n")
            } else {
                area.products.forEach { product ->
                    if (product.name1.isNotEmpty()) {
                        content.append("${product.name1}: ${product.quantity1}\n")
                    }
                    if (product.name2.isNotEmpty()) {
                        content.append("${product.name2}: ${product.quantity2}\n")
                    }
                }
            }

            content.append("\n")
        }

        return content.toString()
    }
}
