package com.wanzijiejie.app.ui.activities

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.ArrayAdapter
import android.widget.Button
import android.widget.ListView
import android.widget.Spinner
import android.widget.TextView
import android.widget.Toast
import com.wanzijiejie.app.R
import com.wanzijiejie.app.ui.screens.Area
import com.wanzijiejie.app.utils.ProductCategoryData
import java.io.Serializable

/**
 * 批量分类活动
 * 用于批量分类未分类的商品
 * 
 * 注意：这个活动会一直显示，直到所有未分类商品都被分类完毕，或者用户点击取消按钮
 */
class BatchClassifyActivity : Activity() {
    // 区域列表
    private lateinit var areas: List<Area>
    
    // 未分类商品列表
    private lateinit var unclassifiedItems: List<Pair<String, String>>
    
    // 剩余未分类商品
    private val remainingItems = mutableListOf<String>()
    
    // 已分类商品
    private val classifiedItems = mutableMapOf<Int, MutableList<Pair<String, String>>>()
    
    // 商品列表适配器
    private lateinit var itemsAdapter: ArrayAdapter<String>
    
    // 商品列表视图
    private lateinit var itemsListView: ListView
    
    // 区域选择器
    private lateinit var areaSpinner: Spinner
    
    // 确认按钮
    private lateinit var confirmButton: Button
    
    // 取消按钮
    private lateinit var cancelButton: Button
    
    // 标题文本视图
    private lateinit var titleTextView: TextView
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_batch_classify)
        
        // 获取区域列表
        areas = intent.getSerializableExtra(EXTRA_AREAS) as List<Area>
        
        // 获取未分类商品列表
        @Suppress("UNCHECKED_CAST")
        unclassifiedItems = intent.getSerializableExtra(EXTRA_UNCLASSIFIED_ITEMS) as List<Pair<String, String>>
        
        // 初始化剩余未分类商品
        remainingItems.addAll(unclassifiedItems.map { it.first })
        
        // 初始化视图
        initViews()
        
        // 设置监听器
        setupListeners()
    }
    
    /**
     * 初始化视图
     */
    private fun initViews() {
        // 获取标题文本视图
        titleTextView = findViewById(R.id.titleTextView)
        
        // 获取商品列表视图
        itemsListView = findViewById(R.id.itemsListView)
        
        // 获取区域选择器
        areaSpinner = findViewById(R.id.areaSpinner)
        
        // 获取确认按钮
        confirmButton = findViewById(R.id.confirmButton)
        
        // 获取取消按钮
        cancelButton = findViewById(R.id.cancelButton)
        
        // 创建商品列表适配器
        itemsAdapter = ArrayAdapter(this, android.R.layout.simple_list_item_multiple_choice, remainingItems)
        
        // 设置商品列表适配器
        itemsListView.adapter = itemsAdapter
        
        // 设置商品列表可多选
        itemsListView.choiceMode = ListView.CHOICE_MODE_MULTIPLE
        
        // 创建区域列表适配器
        val areaAdapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, areas.map { it.name })
        
        // 设置区域列表下拉样式
        areaAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        
        // 设置区域列表适配器
        areaSpinner.adapter = areaAdapter
        
        // 设置区域列表默认选择"其他"
        val otherIndex = areas.indexOfFirst { it.name == "其他" }
        if (otherIndex >= 0) {
            areaSpinner.setSelection(otherIndex)
        }
        
        // 更新标题
        updateTitle()
    }
    
    /**
     * 设置监听器
     */
    private fun setupListeners() {
        // 设置确认按钮点击事件
        confirmButton.setOnClickListener {
            val selectedAreaIndex = areaSpinner.selectedItemPosition
            
            // 获取选中的商品
            val selectedItems = mutableListOf<String>()
            for (i in 0 until itemsListView.count) {
                if (itemsListView.isItemChecked(i)) {
                    selectedItems.add(remainingItems[i])
                }
            }
            
            // 如果没有选中商品，则选中当前显示的第一个商品
            if (selectedItems.isEmpty() && remainingItems.isNotEmpty()) {
                selectedItems.add(remainingItems[0])
                // 选中第一个商品
                itemsListView.setItemChecked(0, true)
            }
            
            // 对每个选中的商品进行分类
            for (item in selectedItems) {
                // 打印分类信息，便于调试
                Toast.makeText(this, "将商品 '$item' 添加到 '${areas[selectedAreaIndex].name}' 区域", Toast.LENGTH_SHORT).show()
                
                // 将商品添加到已分类商品列表
                classifyItem(item, selectedAreaIndex)
                
                // 从剩余列表中移除已分类的商品
                remainingItems.remove(item)
            }
            
            // 更新商品列表
            itemsAdapter.clear()
            itemsAdapter.addAll(remainingItems)
            itemsAdapter.notifyDataSetChanged()
            
            // 只有当没有剩余商品时，才关闭活动
            if (remainingItems.isEmpty()) {
                finishWithResult()
            } else {
                // 清除所有选中状态，准备下一轮选择
                for (i in 0 until itemsListView.count) {
                    itemsListView.setItemChecked(i, false)
                }
                
                // 更新标题
                updateTitle()
                
                // 打印剩余未分类商品信息，便于调试
                Toast.makeText(this, "还有 ${remainingItems.size} 个未分类商品", Toast.LENGTH_SHORT).show()
            }
        }
        
        // 设置取消按钮点击事件
        cancelButton.setOnClickListener {
            // 将所有未分类的商品添加到"其他"区域
            if (remainingItems.isNotEmpty()) {
                val otherIndex = areas.indexOfFirst { it.name == "其他" }
                if (otherIndex >= 0) {
                    // 打印分类信息，便于调试
                    Toast.makeText(this, "将剩余 ${remainingItems.size} 个未分类商品添加到 '其他' 区域", Toast.LENGTH_SHORT).show()
                    
                    // 创建副本以避免循环中修改集合
                    val itemsToProcess = remainingItems.toList()
                    for (item in itemsToProcess) {
                        classifyItem(item, otherIndex)
                        remainingItems.remove(item)
                    }
                }
            }
            
            // 关闭活动
            finishWithResult()
        }
    }
    
    /**
     * 将商品分类到指定区域
     * 
     * @param itemName 商品名称
     * @param areaIndex 区域索引
     */
    private fun classifyItem(itemName: String, areaIndex: Int) {
        // 找到对应的商品和数量
        val item = unclassifiedItems.find { it.first == itemName }
        if (item != null) {
            // 将商品添加到已分类商品列表
            if (!classifiedItems.containsKey(areaIndex)) {
                classifiedItems[areaIndex] = mutableListOf()
            }
            classifiedItems[areaIndex]?.add(item)
            
            // 将商品添加到选定区域的分类中
            val selectedAreaName = areas[areaIndex].name
            ProductCategoryData.addItemToCategory(itemName, selectedAreaName, this)
        }
    }
    
    /**
     * 更新标题
     */
    private fun updateTitle() {
        if (remainingItems.isEmpty()) {
            titleTextView.text = getString(R.string.unclassified_items_title)
        } else {
            titleTextView.text = getString(R.string.unclassified_items_remaining, remainingItems.size)
        }
    }
    
    /**
     * 关闭活动并返回结果
     */
    private fun finishWithResult() {
        // 保存分类数据
        ProductCategoryData.saveCategories(this)
        
        // 创建结果意图
        val resultIntent = Intent()
        resultIntent.putExtra(EXTRA_CLASSIFIED_ITEMS, classifiedItems as Serializable)
        
        // 设置结果
        setResult(RESULT_OK, resultIntent)
        
        // 关闭活动
        finish()
    }
    
    companion object {
        // 请求码
        const val REQUEST_CODE = 1001
        
        // 区域列表额外数据键
        private const val EXTRA_AREAS = "extra_areas"
        
        // 未分类商品列表额外数据键
        private const val EXTRA_UNCLASSIFIED_ITEMS = "extra_unclassified_items"
        
        // 已分类商品列表额外数据键
        const val EXTRA_CLASSIFIED_ITEMS = "extra_classified_items"
        
        /**
         * 创建启动意图
         * 
         * @param context 上下文
         * @param areas 区域列表
         * @param unclassifiedItems 未分类商品列表
         * @return 启动意图
         */
        fun createIntent(context: Context, areas: List<Area>, unclassifiedItems: List<Pair<String, String>>): Intent {
            val intent = Intent(context, BatchClassifyActivity::class.java)
            intent.putExtra(EXTRA_AREAS, areas as Serializable)
            intent.putExtra(EXTRA_UNCLASSIFIED_ITEMS, unclassifiedItems as Serializable)
            return intent
        }
    }
}
