package com.wanzijiejie.app.ui.screens

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.LinearOutSlowInEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.scaleIn
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.ui.graphics.Brush
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.wanzijiejie.app.R
import kotlinx.coroutines.delay
import com.wanzijiejie.app.ui.screens.AutoOrderHelper

@Composable
fun SplashScreen(
    onSplashFinished: () -> Unit,
    modifier: Modifier = Modifier
) {
    // 动画状态
    var startAnimation by remember { mutableStateOf(false) }
    val alphaAnim = animateFloatAsState(
        targetValue = if (startAnimation) 1f else 0f,
        animationSpec = tween(
            durationMillis = 1000,
            easing = FastOutSlowInEasing
        ),
        label = "alphaAnimation"
    )

    // 脉动动画
    val infiniteTransition = rememberInfiniteTransition(label = "pulseAnimation")
    val scale by infiniteTransition.animateFloat(
        initialValue = 0.95f,
        targetValue = 1.05f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = LinearOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "scaleAnimation"
    )

    // 获取当前上下文
    val context = LocalContext.current

    // 启动动画
    LaunchedEffect(key1 = true) {
        startAnimation = true
        delay(4500) // 延长显示时间到4.5秒，确保所有动画效果都能完成

        // 根据用户设置决定是否自动执行报货功能
        val autoOrderOnStartup = AutoOrderHelper.getAutoOrderOnStartup(context)
        AutoOrderHelper.autoClickOrderButton = autoOrderOnStartup

        onSplashFinished()
    }

    // 启动屏幕内容
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(
                // 使用渐变背景
                brush = androidx.compose.ui.graphics.Brush.verticalGradient(
                    colors = listOf(
                        MaterialTheme.colorScheme.background,
                        MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                    )
                )
            ),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
                .alpha(alphaAnim.value)
        ) {
            // 应用图标
            Image(
                painter = painterResource(id = R.drawable.logo),
                contentDescription = "App Logo",
                modifier = Modifier
                    .size(150.dp)
                    .scale(scale)
            )

            Spacer(modifier = Modifier.height(24.dp))

            // 应用名称
            AnimatedVisibility(
                visible = startAnimation,
                enter = fadeIn(animationSpec = tween(1000)) +
                        slideInVertically(
                            animationSpec = tween(1000),
                            initialOffsetY = { it / 2 }
                        ),
                exit = fadeOut()
            ) {
                Text(
                    text = "丸子姐姐",
                    style = MaterialTheme.typography.headlineLarge,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.primary
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 副标题
            AnimatedVisibility(
                visible = startAnimation,
                enter = fadeIn(animationSpec = tween(1200)) +
                        slideInVertically(
                            animationSpec = tween(1200),
                            initialOffsetY = { it / 2 }
                        ),
                exit = fadeOut()
            ) {
                Text(
                    text = "报表管理系统",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Medium,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.secondary
                )
            }

            Spacer(modifier = Modifier.height(32.dp))

            // 加载指示器
            AnimatedVisibility(
                visible = startAnimation,
                enter = fadeIn(animationSpec = tween(1500))
            ) {
                CircularProgressIndicator(
                    modifier = Modifier.size(48.dp),
                    color = MaterialTheme.colorScheme.primary,
                    strokeWidth = 4.dp
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 版本信息
            AnimatedVisibility(
                visible = startAnimation,
                enter = fadeIn(animationSpec = tween(1800))
            ) {
                Text(
                    text = "版本 1.0",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                )
            }

            Spacer(modifier = Modifier.height(32.dp))

            // 底部署名和标志
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.padding(bottom = 16.dp)
            ) {
                // 品牌行
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center
                ) {
                    // 圆圈中的C符号 - 动态效果
                    AnimatedVisibility(
                        visible = startAnimation,
                        enter = fadeIn(animationSpec = tween(durationMillis = 500, delayMillis = 1600)) +
                                scaleIn(
                                    animationSpec = tween(
                                        durationMillis = 700,
                                        delayMillis = 1600,
                                        easing = FastOutSlowInEasing
                                    ),
                                    initialScale = 0.1f
                                )
                    ) {
                        Box(
                            contentAlignment = Alignment.Center,
                            modifier = Modifier
                                .size(24.dp)
                                .background(
                                    color = MaterialTheme.colorScheme.primary,
                                    shape = CircleShape
                                )
                        ) {
                            Text(
                                text = "C",
                                style = MaterialTheme.typography.labelMedium,
                                fontWeight = FontWeight.Bold,
                                color = androidx.compose.ui.graphics.Color.White
                            )
                        }
                    }

                    Spacer(modifier = Modifier.width(8.dp))

                    // AceBrand署名 - 动态高端效果
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        // 逐个字母动画显示
                        val brandName = "AceBrand"
                        brandName.forEachIndexed { index, char ->
                            // 更短的延迟，确保动画能够完成
                            val delay = 1600 + (index * 120)

                            // 使用remember记住每个字母的动画状态
                            var visible by remember { mutableStateOf(false) }

                            // 启动每个字母的动画
                            LaunchedEffect(key1 = startAnimation) {
                                if (startAnimation) {
                                    delay(delay.toLong())
                                    visible = true
                                }
                            }

                            AnimatedVisibility(
                                visible = visible,
                                enter = fadeIn(animationSpec = tween(durationMillis = 300)) +
                                        scaleIn(
                                            animationSpec = tween(
                                                durationMillis = 400,
                                                easing = FastOutSlowInEasing
                                            ),
                                            initialScale = 0.2f
                                        ) +
                                        slideInVertically(
                                            animationSpec = tween(
                                                durationMillis = 400,
                                                easing = FastOutSlowInEasing
                                            ),
                                            initialOffsetY = { it / 2 }
                                        )
                            ) {
                                Text(
                                    text = char.toString(),
                                    style = MaterialTheme.typography.titleLarge, // 使用更大的字体
                                    fontWeight = if (index == 0 || index == 3) FontWeight.Bold else FontWeight.Medium,
                                    color = if (index == 0 || index == 3)
                                        MaterialTheme.colorScheme.primary
                                    else
                                        MaterialTheme.colorScheme.primary.copy(alpha = 0.9f)
                                )
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                // 版权信息 - 动态效果
                AnimatedVisibility(
                    visible = startAnimation,
                    enter = fadeIn(animationSpec = tween(durationMillis = 800, delayMillis = 2800)) +
                            slideInVertically(
                                animationSpec = tween(
                                    durationMillis = 800,
                                    delayMillis = 2800,
                                    easing = FastOutSlowInEasing
                                ),
                                initialOffsetY = { it / 4 }
                            )
                ) {
                    Text(
                        text = "© 2025 wanzijiejie",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                    )
                }
            }
        }
    }
}
