package com.wanzijiejie.app.ui.screens

import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.wanzijiejie.app.R
import com.wanzijiejie.app.data.ProductCategoryData
import com.wanzijiejie.app.services.MusicPlayerService

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CategoryManagementScreen(
    navController: NavController,
    modifier: Modifier = Modifier,
    musicPlayerService: MusicPlayerService? = null
) {
    val context = LocalContext.current
    val focusManager = LocalFocusManager.current
    val focusRequester = remember { FocusRequester() }

    // 加载商品分类数据在下面直接调用

    // 当前选中的区域
    var selectedCategory by remember { mutableStateOf("饭堂") }

    // 新商品名称
    var newItemName by remember { mutableStateOf("") }

    // 编辑模式
    var editMode by remember { mutableStateOf(false) }

    // 当前编辑的商品
    var editingItem by remember { mutableStateOf("") }
    var originalItem by remember { mutableStateOf("") }

    // 搜索功能
    var searchQuery by remember { mutableStateOf("") }

    // 批量操作模式
    var batchMode by remember { mutableStateOf(false) }
    val selectedItems = remember { mutableStateListOf<String>() }

    // 对话框控制
    var showDeleteConfirmDialog by remember { mutableStateOf(false) }
    var showMoveDialog by remember { mutableStateOf(false) }
    var targetCategory by remember { mutableStateOf("") }

    // 搜索控制
    var showSearch by remember { mutableStateOf(false) }

    // 加载商品分类数据
    ProductCategoryData.loadCategories(context)

    // 创建可观察的商品列表，并立即初始化
    val canteenItems = remember {
        mutableStateListOf<String>().apply {
            addAll(ProductCategoryData.canteenItems)
        }
    }
    val spicyItems = remember {
        mutableStateListOf<String>().apply {
            addAll(ProductCategoryData.spicyItems)
        }
    }
    val frozenItems = remember {
        mutableStateListOf<String>().apply {
            addAll(ProductCategoryData.frozenItems)
        }
    }
    val otherItems = remember {
        mutableStateListOf<String>().apply {
            addAll(ProductCategoryData.otherItems)
        }
    }
    val specialItems = remember {
        mutableStateListOf<String>().apply {
            addAll(ProductCategoryData.specialItems)
        }
    }

    // 获取当前区域的商品列表 - 使用derivedStateOf使其成为可观察的
    val currentItems = remember(selectedCategory) {
        when (selectedCategory) {
            "饭堂" -> canteenItems
            "麻辣" -> spicyItems
            "冻货" -> frozenItems
            "其他" -> otherItems
            "特殊" -> specialItems
            else -> mutableStateListOf()
        }
    }

    // 监听选中区域的变化，重置编辑状态
    LaunchedEffect(selectedCategory) {
        editMode = false
        editingItem = ""
        originalItem = ""
        newItemName = ""
        searchQuery = ""
        selectedItems.clear()
        batchMode = false
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.category_management)) },
                navigationIcon = {
                    IconButton(onClick = {
                        if (batchMode) {
                            // 退出批量模式
                            batchMode = false
                            selectedItems.clear()
                        } else {
                            navController.popBackStack()
                        }
                    }) {
                        Icon(
                            if (batchMode) Icons.Default.Close else Icons.Default.ArrowBack,
                            contentDescription = if (batchMode) stringResource(R.string.exit_batch_mode) else stringResource(R.string.cancel)
                        )
                    }
                },
                actions = {
                    // 批量操作模式下显示的操作
                    if (batchMode) {
                        // 选择计数
                        Text(
                            text = stringResource(R.string.selected_count, selectedItems.size),
                            modifier = Modifier.padding(end = 8.dp)
                        )

                        // 全选/取消全选
                        IconButton(
                            onClick = {
                                if (selectedItems.size < currentItems.size) {
                                    // 全选
                                    selectedItems.clear()
                                    selectedItems.addAll(currentItems)
                                } else {
                                    // 取消全选
                                    selectedItems.clear()
                                }
                            }
                        ) {
                            Icon(
                                imageVector = if (selectedItems.size == currentItems.size && currentItems.isNotEmpty())
                                    Icons.Default.Check
                                else
                                    Icons.Default.Clear,
                                contentDescription = if (selectedItems.size == currentItems.size)
                                    stringResource(R.string.deselect_all)
                                else
                                    stringResource(R.string.select_all)
                            )
                        }

                        // 删除所选
                        IconButton(
                            onClick = {
                                if (selectedItems.isNotEmpty()) {
                                    // 显示确认对话框
                                    showDeleteConfirmDialog = true
                                } else {
                                    Toast.makeText(context, context.getString(R.string.no_items_selected), Toast.LENGTH_SHORT).show()
                                }
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Delete,
                                contentDescription = stringResource(R.string.delete_selected)
                            )
                        }

                        // 移动所选
                        IconButton(
                            onClick = {
                                if (selectedItems.isNotEmpty()) {
                                    // 显示移动对话框
                                    showMoveDialog = true
                                } else {
                                    Toast.makeText(context, context.getString(R.string.no_items_selected), Toast.LENGTH_SHORT).show()
                                }
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Send,
                                contentDescription = stringResource(R.string.move_selected)
                            )
                        }
                    } else {
                        // 搜索按钮
                        IconButton(
                            onClick = { showSearch = !showSearch }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Search,
                                contentDescription = stringResource(R.string.search)
                            )
                        }

                        // 批量操作按钮
                        IconButton(
                            onClick = {
                                batchMode = true
                                editMode = false
                                editingItem = ""
                                originalItem = ""
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Default.List,
                                contentDescription = stringResource(R.string.batch_mode)
                            )
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
        ) {
            // 搜索栏
            if (showSearch) {
                OutlinedTextField(
                    value = searchQuery,
                    onValueChange = { searchQuery = it },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 8.dp),
                    placeholder = { Text(stringResource(R.string.search_hint)) },
                    leadingIcon = { Icon(Icons.Default.Search, contentDescription = null) },
                    trailingIcon = {
                        if (searchQuery.isNotEmpty()) {
                            IconButton(onClick = { searchQuery = "" }) {
                                Icon(Icons.Default.Clear, contentDescription = null)
                            }
                        }
                    },
                    singleLine = true,
                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Search),
                    keyboardActions = KeyboardActions(onSearch = { focusManager.clearFocus() })
                )
            }
            // 区域选择
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = stringResource(R.string.select_area),
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Column(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        // 第一行：饭堂和其他
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            CategoryButton(
                                text = stringResource(R.string.canteen),
                                selected = selectedCategory == "饭堂",
                                onClick = { selectedCategory = "饭堂" },
                                modifier = Modifier.weight(1f)
                            )

                            Spacer(modifier = Modifier.width(8.dp))

                            CategoryButton(
                                text = stringResource(R.string.others),
                                selected = selectedCategory == "其他",
                                onClick = { selectedCategory = "其他" },
                                modifier = Modifier.weight(1f)
                            )
                        }

                        Spacer(modifier = Modifier.height(8.dp))

                        // 第二行：麻辣和特殊
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            CategoryButton(
                                text = stringResource(R.string.spicy),
                                selected = selectedCategory == "麻辣",
                                onClick = { selectedCategory = "麻辣" },
                                modifier = Modifier.weight(1f)
                            )

                            Spacer(modifier = Modifier.width(8.dp))

                            CategoryButton(
                                text = stringResource(R.string.special),
                                selected = selectedCategory == "特殊",
                                onClick = { selectedCategory = "特殊" },
                                modifier = Modifier.weight(1f)
                            )
                        }

                        Spacer(modifier = Modifier.height(8.dp))

                        // 第三行：冻货
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            CategoryButton(
                                text = stringResource(R.string.frozen),
                                selected = selectedCategory == "冻货",
                                onClick = { selectedCategory = "冻货" },
                                modifier = Modifier.weight(1f)
                            )
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 添加/编辑商品
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = if (editMode) stringResource(R.string.edit_item) else stringResource(R.string.add_item),
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        OutlinedTextField(
                            value = if (editMode) editingItem else newItemName,
                            onValueChange = {
                                if (editMode) {
                                    editingItem = it
                                } else {
                                    newItemName = it
                                }
                            },
                            label = { Text(if (editMode) stringResource(R.string.edit_item_name) else stringResource(R.string.input_item_name)) },
                            modifier = Modifier
                                .weight(1f)
                                .focusRequester(focusRequester),
                            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                            keyboardActions = KeyboardActions(
                                onDone = {
                                    if (editMode) {
                                        if (editingItem.isNotBlank()) {
                                            // 更新商品名称
                                            val index = currentItems.indexOf(originalItem)
                                            if (index != -1) {
                                                // 更新ProductCategoryData中的列表
                                                when (selectedCategory) {
                                                    "饭堂" -> {
                                                        val dataIndex = ProductCategoryData.canteenItems.indexOf(originalItem)
                                                        if (dataIndex != -1) {
                                                            ProductCategoryData.canteenItems[dataIndex] = editingItem
                                                            // 更新本地状态列表中的对应项
                                                            val localIndex = canteenItems.indexOf(originalItem)
                                                            if (localIndex != -1) {
                                                                canteenItems[localIndex] = editingItem
                                                            }
                                                        }
                                                    }
                                                    "麻辣" -> {
                                                        val dataIndex = ProductCategoryData.spicyItems.indexOf(originalItem)
                                                        if (dataIndex != -1) {
                                                            ProductCategoryData.spicyItems[dataIndex] = editingItem
                                                            // 更新本地状态列表中的对应项
                                                            val localIndex = spicyItems.indexOf(originalItem)
                                                            if (localIndex != -1) {
                                                                spicyItems[localIndex] = editingItem
                                                            }
                                                        }
                                                    }
                                                    "冻货" -> {
                                                        val dataIndex = ProductCategoryData.frozenItems.indexOf(originalItem)
                                                        if (dataIndex != -1) {
                                                            ProductCategoryData.frozenItems[dataIndex] = editingItem
                                                            // 更新本地状态列表中的对应项
                                                            val localIndex = frozenItems.indexOf(originalItem)
                                                            if (localIndex != -1) {
                                                                frozenItems[localIndex] = editingItem
                                                            }
                                                        }
                                                    }
                                                    "其他" -> {
                                                        val dataIndex = ProductCategoryData.otherItems.indexOf(originalItem)
                                                        if (dataIndex != -1) {
                                                            ProductCategoryData.otherItems[dataIndex] = editingItem
                                                            // 更新本地状态列表中的对应项
                                                            val localIndex = otherItems.indexOf(originalItem)
                                                            if (localIndex != -1) {
                                                                otherItems[localIndex] = editingItem
                                                            }
                                                        }
                                                    }
                                                    "特殊" -> {
                                                        val dataIndex = ProductCategoryData.specialItems.indexOf(originalItem)
                                                        if (dataIndex != -1) {
                                                            ProductCategoryData.specialItems[dataIndex] = editingItem
                                                            // 更新本地状态列表中的对应项
                                                            val localIndex = specialItems.indexOf(originalItem)
                                                            if (localIndex != -1) {
                                                                specialItems[localIndex] = editingItem
                                                            }
                                                        }
                                                    }
                                                }

                                                // 保存更改
                                                ProductCategoryData.saveCategories(context)
                                                Toast.makeText(context, context.getString(R.string.item_updated), Toast.LENGTH_SHORT).show()
                                            }
                                            editMode = false
                                            editingItem = ""
                                            originalItem = ""
                                        }
                                    } else {
                                        if (newItemName.isNotBlank()) {
                                            // 添加新商品
                                            if (!currentItems.contains(newItemName)) {
                                                // 更新ProductCategoryData中的列表
                                                when (selectedCategory) {
                                                    "饭堂" -> {
                                                        ProductCategoryData.canteenItems.add(newItemName)
                                                        canteenItems.add(newItemName)
                                                    }
                                                    "麻辣" -> {
                                                        ProductCategoryData.spicyItems.add(newItemName)
                                                        spicyItems.add(newItemName)
                                                    }
                                                    "冻货" -> {
                                                        ProductCategoryData.frozenItems.add(newItemName)
                                                        frozenItems.add(newItemName)
                                                    }
                                                    "其他" -> {
                                                        ProductCategoryData.otherItems.add(newItemName)
                                                        otherItems.add(newItemName)
                                                    }
                                                    "特殊" -> {
                                                        ProductCategoryData.specialItems.add(newItemName)
                                                        specialItems.add(newItemName)
                                                    }
                                                }

                                                // 保存更改
                                                ProductCategoryData.saveCategories(context)
                                                Toast.makeText(context, context.getString(R.string.item_added), Toast.LENGTH_SHORT).show()
                                                newItemName = ""
                                            } else {
                                                Toast.makeText(context, context.getString(R.string.item_exists), Toast.LENGTH_SHORT).show()
                                            }
                                        }
                                    }
                                    focusManager.clearFocus()
                                }
                            )
                        )

                        Spacer(modifier = Modifier.width(8.dp))

                        Button(
                            onClick = {
                                if (editMode) {
                                    if (editingItem.isNotBlank()) {
                                        // 更新商品名称
                                        val index = currentItems.indexOf(originalItem)
                                        if (index != -1) {
                                            // 更新ProductCategoryData中的列表
                                            when (selectedCategory) {
                                                "饭堂" -> {
                                                    val dataIndex = ProductCategoryData.canteenItems.indexOf(originalItem)
                                                    if (dataIndex != -1) {
                                                        ProductCategoryData.canteenItems[dataIndex] = editingItem
                                                        // 更新本地状态列表中的对应项
                                                        val localIndex = canteenItems.indexOf(originalItem)
                                                        if (localIndex != -1) {
                                                            canteenItems[localIndex] = editingItem
                                                        }
                                                    }
                                                }
                                                "麻辣" -> {
                                                    val dataIndex = ProductCategoryData.spicyItems.indexOf(originalItem)
                                                    if (dataIndex != -1) {
                                                        ProductCategoryData.spicyItems[dataIndex] = editingItem
                                                        // 更新本地状态列表中的对应项
                                                        val localIndex = spicyItems.indexOf(originalItem)
                                                        if (localIndex != -1) {
                                                            spicyItems[localIndex] = editingItem
                                                        }
                                                    }
                                                }
                                                "冻货" -> {
                                                    val dataIndex = ProductCategoryData.frozenItems.indexOf(originalItem)
                                                    if (dataIndex != -1) {
                                                        ProductCategoryData.frozenItems[dataIndex] = editingItem
                                                        // 更新本地状态列表中的对应项
                                                        val localIndex = frozenItems.indexOf(originalItem)
                                                        if (localIndex != -1) {
                                                            frozenItems[localIndex] = editingItem
                                                        }
                                                    }
                                                }
                                                "其他" -> {
                                                    val dataIndex = ProductCategoryData.otherItems.indexOf(originalItem)
                                                    if (dataIndex != -1) {
                                                        ProductCategoryData.otherItems[dataIndex] = editingItem
                                                        // 更新本地状态列表中的对应项
                                                        val localIndex = otherItems.indexOf(originalItem)
                                                        if (localIndex != -1) {
                                                            otherItems[localIndex] = editingItem
                                                        }
                                                    }
                                                }
                                                "特殊" -> {
                                                    val dataIndex = ProductCategoryData.specialItems.indexOf(originalItem)
                                                    if (dataIndex != -1) {
                                                        ProductCategoryData.specialItems[dataIndex] = editingItem
                                                        // 更新本地状态列表中的对应项
                                                        val localIndex = specialItems.indexOf(originalItem)
                                                        if (localIndex != -1) {
                                                            specialItems[localIndex] = editingItem
                                                        }
                                                    }
                                                }
                                            }

                                            // 保存更改
                                            ProductCategoryData.saveCategories(context)
                                            Toast.makeText(context, context.getString(R.string.item_updated), Toast.LENGTH_SHORT).show()
                                        }
                                        editMode = false
                                        editingItem = ""
                                        originalItem = ""
                                    }
                                } else {
                                    if (newItemName.isNotBlank()) {
                                        // 添加新商品
                                        if (!currentItems.contains(newItemName)) {
                                            // 更新ProductCategoryData中的列表
                                            when (selectedCategory) {
                                                "饭堂" -> {
                                                    ProductCategoryData.canteenItems.add(newItemName)
                                                    canteenItems.add(newItemName)
                                                }
                                                "麻辣" -> {
                                                    ProductCategoryData.spicyItems.add(newItemName)
                                                    spicyItems.add(newItemName)
                                                }
                                                "冻货" -> {
                                                    ProductCategoryData.frozenItems.add(newItemName)
                                                    frozenItems.add(newItemName)
                                                }
                                                "其他" -> {
                                                    ProductCategoryData.otherItems.add(newItemName)
                                                    otherItems.add(newItemName)
                                                }
                                                "特殊" -> {
                                                    ProductCategoryData.specialItems.add(newItemName)
                                                    specialItems.add(newItemName)
                                                }
                                            }

                                            // 保存更改
                                            ProductCategoryData.saveCategories(context)
                                            Toast.makeText(context, context.getString(R.string.item_added), Toast.LENGTH_SHORT).show()
                                            newItemName = ""
                                        } else {
                                            Toast.makeText(context, context.getString(R.string.item_exists), Toast.LENGTH_SHORT).show()
                                        }
                                    }
                                }
                                focusManager.clearFocus()
                            }
                        ) {
                            Text(if (editMode) stringResource(R.string.save_item) else stringResource(R.string.add_item))
                        }

                        if (editMode) {
                            Spacer(modifier = Modifier.width(8.dp))

                            Button(
                                onClick = {
                                    editMode = false
                                    editingItem = ""
                                    originalItem = ""
                                    focusManager.clearFocus()
                                },
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = MaterialTheme.colorScheme.error
                                )
                            ) {
                                Text(stringResource(R.string.cancel_edit))
                            }
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 商品列表
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = stringResource(R.string.item_list, selectedCategory),
                            style = MaterialTheme.typography.titleLarge,
                            fontWeight = FontWeight.Bold
                        )

                        Text(
                            text = stringResource(R.string.item_count, currentItems.size),
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    // 过滤商品列表 - 不使用remember缓存，确保每次重组都重新计算
                    val filteredItems = if (searchQuery.isEmpty()) {
                        currentItems.toList().sorted()
                    } else {
                        currentItems.filter {
                            it.contains(searchQuery, ignoreCase = true)
                        }.sorted()
                    }

                    if (filteredItems.isEmpty() && searchQuery.isNotEmpty()) {
                        // 显示无搜索结果提示
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f)
                                .padding(16.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = stringResource(R.string.no_search_results),
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    } else {
                        // 显示商品列表
                        LazyColumn(
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f)
                        ) {
                            items(filteredItems) { item ->
                                if (batchMode) {
                                    // 批量操作模式下的商品行
                                    BatchItemRow(
                                        item = item,
                                        isSelected = selectedItems.contains(item),
                                        onToggleSelection = {
                                            if (selectedItems.contains(item)) {
                                                selectedItems.remove(item)
                                            } else {
                                                selectedItems.add(item)
                                            }
                                        }
                                    )
                                } else {
                                    // 普通模式下的商品行
                                    ItemRow(
                                        item = item,
                                        onEdit = {
                                            editMode = true
                                            editingItem = item
                                            originalItem = item
                                            focusRequester.requestFocus()
                                        },
                                        onDelete = {
                                            // 从ProductCategoryData中删除
                                            when (selectedCategory) {
                                                "饭堂" -> {
                                                    // 直接从列表中删除指定项
                                                    ProductCategoryData.canteenItems.remove(item)
                                                    canteenItems.remove(item)
                                                }
                                                "麻辣" -> {
                                                    ProductCategoryData.spicyItems.remove(item)
                                                    spicyItems.remove(item)
                                                }
                                                "冻货" -> {
                                                    ProductCategoryData.frozenItems.remove(item)
                                                    frozenItems.remove(item)
                                                }
                                                "其他" -> {
                                                    ProductCategoryData.otherItems.remove(item)
                                                    otherItems.remove(item)
                                                }
                                                "特殊" -> {
                                                    ProductCategoryData.specialItems.remove(item)
                                                    specialItems.remove(item)
                                                }
                                            }

                                            // 保存更改
                                            ProductCategoryData.saveCategories(context)
                                            Toast.makeText(context, context.getString(R.string.item_deleted), Toast.LENGTH_SHORT).show()
                                        }
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // 批量删除确认对话框
    if (showDeleteConfirmDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteConfirmDialog = false },
            title = { Text(stringResource(R.string.confirm_delete)) },
            text = { Text(stringResource(R.string.delete_confirm_multiple, selectedItems.size)) },
            confirmButton = {
                TextButton(
                    onClick = {
                        // 创建要删除的商品列表副本
                        val itemsToDelete = selectedItems.toList()

                        // 从ProductCategoryData和本地状态列表中删除
                        when (selectedCategory) {
                            "饭堂" -> {
                                itemsToDelete.forEach { item ->
                                    ProductCategoryData.canteenItems.remove(item)
                                    canteenItems.remove(item)
                                }
                            }
                            "麻辣" -> {
                                itemsToDelete.forEach { item ->
                                    ProductCategoryData.spicyItems.remove(item)
                                    spicyItems.remove(item)
                                }
                            }
                            "冻货" -> {
                                itemsToDelete.forEach { item ->
                                    ProductCategoryData.frozenItems.remove(item)
                                    frozenItems.remove(item)
                                }
                            }
                            "其他" -> {
                                itemsToDelete.forEach { item ->
                                    ProductCategoryData.otherItems.remove(item)
                                    otherItems.remove(item)
                                }
                            }
                            "特殊" -> {
                                itemsToDelete.forEach { item ->
                                    ProductCategoryData.specialItems.remove(item)
                                    specialItems.remove(item)
                                }
                            }
                        }

                        // 保存更改
                        ProductCategoryData.saveCategories(context)
                        Toast.makeText(context, context.getString(R.string.item_deleted), Toast.LENGTH_SHORT).show()

                        // 清空选择并关闭对话框
                        selectedItems.clear()
                        showDeleteConfirmDialog = false
                    }
                ) {
                    Text(stringResource(R.string.confirm))
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDeleteConfirmDialog = false }
                ) {
                    Text(stringResource(R.string.cancel))
                }
            }
        )
    }

    // 批量移动对话框
    if (showMoveDialog) {
        AlertDialog(
            onDismissRequest = { showMoveDialog = false },
            title = { Text(stringResource(R.string.move_to)) },
            text = {
                Column {
                    // 目标区域选择
                    RadioButton(
                        selected = targetCategory == "饭堂",
                        onClick = { targetCategory = "饭堂" },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(stringResource(R.string.canteen))
                    }

                    RadioButton(
                        selected = targetCategory == "麻辣",
                        onClick = { targetCategory = "麻辣" },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(stringResource(R.string.spicy))
                    }

                    RadioButton(
                        selected = targetCategory == "冻货",
                        onClick = { targetCategory = "冻货" },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(stringResource(R.string.frozen))
                    }

                    RadioButton(
                        selected = targetCategory == "其他",
                        onClick = { targetCategory = "其他" },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(stringResource(R.string.others))
                    }

                    RadioButton(
                        selected = targetCategory == "特殊",
                        onClick = { targetCategory = "特殊" },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(stringResource(R.string.special))
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        if (targetCategory.isNotEmpty() && targetCategory != selectedCategory) {
                            // 创建要移动的商品列表副本
                            val itemsToMove = selectedItems.toList()

                            // 从当前分类中删除
                            when (selectedCategory) {
                                "饭堂" -> {
                                    itemsToMove.forEach { item ->
                                        ProductCategoryData.canteenItems.remove(item)
                                        canteenItems.remove(item)
                                    }
                                }
                                "麻辣" -> {
                                    itemsToMove.forEach { item ->
                                        ProductCategoryData.spicyItems.remove(item)
                                        spicyItems.remove(item)
                                    }
                                }
                                "冻货" -> {
                                    itemsToMove.forEach { item ->
                                        ProductCategoryData.frozenItems.remove(item)
                                        frozenItems.remove(item)
                                    }
                                }
                                "其他" -> {
                                    itemsToMove.forEach { item ->
                                        ProductCategoryData.otherItems.remove(item)
                                        otherItems.remove(item)
                                    }
                                }
                                "特殊" -> {
                                    itemsToMove.forEach { item ->
                                        ProductCategoryData.specialItems.remove(item)
                                        specialItems.remove(item)
                                    }
                                }
                            }

                            // 添加到目标分类
                            when (targetCategory) {
                                "饭堂" -> {
                                    itemsToMove.forEach { item ->
                                        if (!ProductCategoryData.canteenItems.contains(item)) {
                                            ProductCategoryData.canteenItems.add(item)
                                            canteenItems.add(item)
                                        }
                                    }
                                }
                                "麻辣" -> {
                                    itemsToMove.forEach { item ->
                                        if (!ProductCategoryData.spicyItems.contains(item)) {
                                            ProductCategoryData.spicyItems.add(item)
                                            spicyItems.add(item)
                                        }
                                    }
                                }
                                "冻货" -> {
                                    itemsToMove.forEach { item ->
                                        if (!ProductCategoryData.frozenItems.contains(item)) {
                                            ProductCategoryData.frozenItems.add(item)
                                            frozenItems.add(item)
                                        }
                                    }
                                }
                                "其他" -> {
                                    itemsToMove.forEach { item ->
                                        if (!ProductCategoryData.otherItems.contains(item)) {
                                            ProductCategoryData.otherItems.add(item)
                                            otherItems.add(item)
                                        }
                                    }
                                }
                                "特殊" -> {
                                    itemsToMove.forEach { item ->
                                        if (!ProductCategoryData.specialItems.contains(item)) {
                                            ProductCategoryData.specialItems.add(item)
                                            specialItems.add(item)
                                        }
                                    }
                                }
                            }

                            // 保存更改
                            ProductCategoryData.saveCategories(context)
                            Toast.makeText(context, context.getString(R.string.move_success), Toast.LENGTH_SHORT).show()

                            // 清空选择并关闭对话框
                            selectedItems.clear()
                            showMoveDialog = false
                            targetCategory = ""
                        } else {
                            showMoveDialog = false
                        }
                    },
                    enabled = targetCategory.isNotEmpty() && targetCategory != selectedCategory
                ) {
                    Text(stringResource(R.string.confirm))
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showMoveDialog = false
                        targetCategory = ""
                    }
                ) {
                    Text(stringResource(R.string.cancel))
                }
            }
        )
    }
}

@Composable
fun RadioButton(
    selected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Row(
        modifier = modifier
            .clickable(onClick = onClick)
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        androidx.compose.material3.RadioButton(
            selected = selected,
            onClick = onClick
        )
        Spacer(modifier = Modifier.width(8.dp))
        content()
    }
}

@Composable
fun BatchItemRow(
    item: String,
    isSelected: Boolean,
    onToggleSelection: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
            .clip(RoundedCornerShape(8.dp))
            .border(1.dp, MaterialTheme.colorScheme.outlineVariant, RoundedCornerShape(8.dp))
            .background(MaterialTheme.colorScheme.surface)
            .clickable(onClick = onToggleSelection)
            .padding(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Checkbox(
            checked = isSelected,
            onCheckedChange = { onToggleSelection() }
        )

        Spacer(modifier = Modifier.width(8.dp))

        Text(
            text = item,
            modifier = Modifier
                .weight(1f)
                .padding(8.dp),
            style = MaterialTheme.typography.bodyLarge
        )
    }
}

@Composable
fun CategoryButton(
    text: String,
    selected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Button(
        onClick = onClick,
        colors = ButtonDefaults.buttonColors(
            containerColor = if (selected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.surfaceVariant,
            contentColor = if (selected) MaterialTheme.colorScheme.onPrimary else MaterialTheme.colorScheme.onSurfaceVariant
        ),
        modifier = modifier.padding(horizontal = 4.dp)
    ) {
        Text(text)
    }
}

@Composable
fun ItemRow(
    item: String,
    onEdit: () -> Unit,
    onDelete: () -> Unit
) {
    var showDeleteConfirm by remember { mutableStateOf(false) }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
            .clip(RoundedCornerShape(8.dp))
            .border(1.dp, MaterialTheme.colorScheme.outlineVariant, RoundedCornerShape(8.dp))
            .background(MaterialTheme.colorScheme.surface)
            .padding(8.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = item,
            modifier = Modifier
                .weight(1f)
                .padding(8.dp),
            style = MaterialTheme.typography.bodyLarge
        )

        Row {
            IconButton(onClick = onEdit) {
                Icon(
                    imageVector = Icons.Default.Edit,
                    contentDescription = stringResource(R.string.edit),
                    tint = MaterialTheme.colorScheme.primary
                )
            }

            IconButton(onClick = { showDeleteConfirm = true }) {
                Icon(
                    imageVector = Icons.Default.Delete,
                    contentDescription = stringResource(R.string.delete),
                    tint = MaterialTheme.colorScheme.error
                )
            }
        }
    }

    if (showDeleteConfirm) {
        AlertDialog(
            onDismissRequest = { showDeleteConfirm = false },
            title = { Text(stringResource(R.string.confirm_delete)) },
            text = { Text(stringResource(R.string.confirm_delete_message, item)) },
            confirmButton = {
                TextButton(
                    onClick = {
                        onDelete()
                        showDeleteConfirm = false
                    }
                ) {
                    Text(stringResource(R.string.confirm))
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDeleteConfirm = false }
                ) {
                    Text(stringResource(R.string.cancel))
                }
            }
        )
    }
}
