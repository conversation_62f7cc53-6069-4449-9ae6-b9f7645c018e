package com.wanzijiejie.app.utils

import android.content.Context
import android.widget.Toast
import com.wanzijiejie.app.R
import com.wanzijiejie.app.data.ProductCategoryData
import com.wanzijiejie.app.ui.screens.Area
import com.wanzijiejie.app.ui.screens.Product

/**
 * 内容解析工具类
 */
object ContentParser {

    /**
     * 解析粘贴的内容
     * @param content 粘贴的内容
     * @return 解析后的商品列表，每个商品包含名称和数量
     */
    fun parsePastedContent(content: String): List<Pair<String, String>> {
        // 使用多种分隔符：空格、换行符、逗号、分号、中文逗号、中文分号、句号、点、制表符等
        val separators = Regex("[\\s,;，；\\.。\t]+")
        val items = mutableListOf<Pair<String, String>>()

        // 分割内容并过滤空项
        val contentItems = content.split(separators).filter { it.isNotBlank() }

        // 如果没有有效项，返回空列表
        if (contentItems.isEmpty()) {
            return emptyList()
        }

        // 处理每个项
        for (item in contentItems) {
            // 首先检查完整的商品名是否在特殊商品列表中
            val fullItemMatch = ProductCategoryData.specialItems.find { it == item.trim() }
            if (fullItemMatch != null) {
                // 如果完整的商品名在特殊商品列表中，则将其作为商品名，数量默认为1
                items.add(Pair(fullItemMatch, "1"))
                continue
            }

            // 检查是否是特殊商品的前缀
            val specialItem = ProductCategoryData.specialItems.find { item.startsWith(it) }
            if (specialItem != null) {
                // 将特殊商品名称作为商品名，剩余部分作为数量
                val itemName = specialItem
                var quantity = item.substring(specialItem.length).trim()

                // 如果数量为空，默认设置为1
                if (quantity.isEmpty()) {
                    quantity = "1"
                }

                items.add(Pair(itemName, quantity))
            } else {
                // 检查是否包含空格，如果包含空格，则按空格分隔商品名和数量
                if (item.contains(" ")) {
                    // 尝试识别常见的数量格式，如"1件"、"2个"、"3袋"、"4把"等
                    val quantityPattern = Regex("(\\d+)(件|个|箱|包|袋|瓶|盒|条|块|只|双|对|套|组|份|待|把)$")
                    val quantityMatch = quantityPattern.find(item)

                    if (quantityMatch != null) {
                        // 如果匹配到了数量格式，则将其前面的部分作为商品名
                        val quantityStartIndex = quantityMatch.range.first
                        val itemName = item.substring(0, quantityStartIndex).trim()
                        val quantity = quantityMatch.groupValues[0] // 完整的数量表示，如"1件"

                        // 如果商品名为空，则整个字符串都是商品名
                        if (itemName.isEmpty()) {
                            items.add(Pair(item.trim(), "1"))
                        } else {
                            items.add(Pair(itemName, quantity))
                        }
                    } else {
                        // 如果没有匹配到数量格式，则按空格分隔
                        val parts = item.split(" ", limit = 2)
                        val itemName = parts[0].trim()
                        var quantity = if (parts.size > 1) parts[1].trim() else "1"

                        // 如果数量部分不是以数字开头，则认为整个字符串都是商品名
                        if (quantity.isEmpty() || !quantity[0].isDigit()) {
                            items.add(Pair(item.trim(), "1"))
                        } else {
                            items.add(Pair(itemName, quantity))
                        }
                    }
                } else {
                    // 尝试识别常见的数量格式，如"1件"、"2个"、"3袋"、"4把"等
                    val quantityPattern = Regex("(\\d+)(件|个|箱|包|袋|瓶|盒|条|块|只|双|对|套|组|份|待|把)$")
                    val quantityMatch = quantityPattern.find(item)

                    if (quantityMatch != null) {
                        // 如果匹配到了数量格式，则将其前面的部分作为商品名
                        val quantityStartIndex = quantityMatch.range.first
                        val itemName = item.substring(0, quantityStartIndex).trim()
                        val quantity = quantityMatch.groupValues[0] // 完整的数量表示，如"1件"

                        // 如果商品名为空，则整个字符串都是商品名
                        if (itemName.isEmpty()) {
                            items.add(Pair(item.trim(), "1"))
                        } else {
                            items.add(Pair(itemName, quantity))
                        }
                    } else {
                        // 如果没有匹配到数量格式，则尝试从末尾提取数字作为数量
                        val match = Regex("^(.+?)(\\d+)$").find(item)
                        if (match != null) {
                            // 检查商品名部分是否为空
                            val itemName = match.groupValues[1].trim()
                            var quantity = match.groupValues[2].trim()

                            // 如果商品名为空，则整个字符串都是商品名
                            if (itemName.isEmpty()) {
                                items.add(Pair(item.trim(), "1"))
                            } else {
                                // 如果数量为空，默认设置为1
                                if (quantity.isEmpty()) {
                                    quantity = "1"
                                }

                                items.add(Pair(itemName, quantity))
                            }
                        } else {
                            // 如果无法匹配，尝试将整个行作为商品名，数量默认1个
                            items.add(Pair(item.trim(), "1"))
                        }
                    }
                }
            }
        }

        return items
    }

    /**
     * 将商品按所属区域进行分组
     * @param items 商品列表
     * @return 包含分类商品和未分类商品的结果
     */
    fun groupItemsBySection(items: List<Pair<String, String>>): Pair<Map<String, List<Pair<String, String>>>, List<Pair<String, String>>> {
        val grouped = mutableMapOf(
            "饭堂" to mutableListOf<Pair<String, String>>(),
            "麻辣" to mutableListOf<Pair<String, String>>(),
            "冻货" to mutableListOf<Pair<String, String>>(),
            "其他" to mutableListOf<Pair<String, String>>()
        )

        // 未分类的商品
        val unclassifiedItems = mutableListOf<Pair<String, String>>()

        items.forEach { (itemName, quantity) ->
            val trimmedName = itemName.trim()
            val category = ProductCategoryData.getCategoryForItem(trimmedName)

            when (category) {
                "饭堂" -> grouped["饭堂"]?.add(Pair(trimmedName, quantity))
                "麻辣" -> grouped["麻辣"]?.add(Pair(trimmedName, quantity))
                "冻货" -> grouped["冻货"]?.add(Pair(trimmedName, quantity))
                "其他" -> grouped["其他"]?.add(Pair(trimmedName, quantity))
                "未分类" -> unclassifiedItems.add(Pair(trimmedName, quantity))
            }
        }

        return Pair(grouped, unclassifiedItems)
    }

    /**
     * 处理粘贴内容并填充到报表中
     * @param content 粘贴的内容
     * @param areas 区域列表
     * @param context 上下文
     * @param mode 处理模式：0=覆盖模式，1=继续添加模式
     * @param onBatchClassifyStart 开始批量分类的回调
     * @param onBatchClassifyComplete 完成批量分类的回调
     * @return 是否成功处理
     */
    fun processPastedContent(
        content: String,
        areas: MutableList<Area>,
        context: Context,
        mode: Int = 0, // 0=覆盖模式，1=继续添加模式
        onBatchClassifyStart: (() -> Unit)? = null,
        onBatchClassifyComplete: (() -> Unit)? = null
    ): Boolean {
        if (content.isBlank()) {
            Toast.makeText(context, "粘贴内容为空", Toast.LENGTH_SHORT).show()
            return false
        }

        // 解析粘贴内容
        val parsedItems = parsePastedContent(content)
        if (parsedItems.isEmpty()) {
            Toast.makeText(context, "未能解析出任何商品", Toast.LENGTH_SHORT).show()
            return false
        }

        // 打印解析出的商品信息，便于调试
        Toast.makeText(context, "解析出商品数量: ${parsedItems.size}", Toast.LENGTH_SHORT).show()

        // 按区域分组
        val (groupedItems, unclassifiedItems) = groupItemsBySection(parsedItems)

        // 打印分组信息，便于调试
        val groupedItemsCount = groupedItems.values.sumOf { it.size }
        val unclassifiedItemsCount = unclassifiedItems.size
        Toast.makeText(context, "已分类商品: $groupedItemsCount, 未分类商品: $unclassifiedItemsCount", Toast.LENGTH_SHORT).show()

        // 将分组后的商品填充到报表中
        val categorizedItems = mutableMapOf<Int, MutableList<Pair<String, String>>>()

        // 将分组后的商品添加到对应区域
        areas.forEachIndexed { index, area ->
            val areaName = area.name
            val items = groupedItems[areaName] ?: emptyList()

            if (items.isNotEmpty()) {
                categorizedItems[index] = items.toMutableList()
            }
        }

        // 处理分类后的商品
        processCategorizedItems(categorizedItems, areas, mode, context)

        // 如果有未分类的商品，则显示批量分类对话框
        if (unclassifiedItems.isNotEmpty()) {
            // 打印未分类商品信息，便于调试
            Toast.makeText(context, "发现 ${unclassifiedItems.size} 个未分类商品", Toast.LENGTH_SHORT).show()

            // 通知开始批量分类
            onBatchClassifyStart?.invoke()

            // 创建并显示批量分类对话框
            // 使用新的BatchClassifyDialog3类，确保对话框不会关闭，除非所有未分类商品都已经分类完毕

            // 创建未分类商品的可变列表，以便在分类过程中移除已分类的商品
            val mutableUnclassifiedItems = unclassifiedItems.toMutableList()

            // 创建对话框实例，但不立即显示
            val batchClassifyDialog = com.wanzijiejie.app.ui.components.BatchClassifyDialog3(
                context = context,
                areas = areas,
                unclassifiedItems = mutableUnclassifiedItems.toList(), // 使用可变列表的副本
                onItemClassified = { itemName, selectedAreaIndex ->
                    // 将商品添加到选定的区域
                    if (selectedAreaIndex >= 0 && selectedAreaIndex < areas.size) {
                        // 找到对应的商品和数量
                        val item = mutableUnclassifiedItems.find { it.first == itemName }
                        if (item != null) {
                            // 打印分类信息，便于调试
                            Toast.makeText(context, "将商品 '${item.first}' 添加到 '${areas[selectedAreaIndex].name}' 区域", Toast.LENGTH_SHORT).show()
                            android.util.Log.d("ContentParser", "将商品 '${item.first}' 添加到 '${areas[selectedAreaIndex].name}' 区域")

                            // 将商品添加到选定的区域
                            if (!categorizedItems.containsKey(selectedAreaIndex)) {
                                categorizedItems[selectedAreaIndex] = mutableListOf()
                            }
                            categorizedItems[selectedAreaIndex]?.add(item)

                            // 将商品添加到选定区域的分类中
                            val selectedAreaName = areas[selectedAreaIndex].name
                            ProductCategoryData.addItemToCategory(itemName, selectedAreaName, context)

                            // 处理新添加的商品
                            val newItems = mapOf<Int, List<Pair<String, String>>>(selectedAreaIndex to listOf(item))
                            processCategorizedItems(newItems, areas, 1)

                            // 从未分类商品列表中移除已分类的商品
                            mutableUnclassifiedItems.remove(item)
                            android.util.Log.d("ContentParser", "从未分类商品列表中移除商品 '${item.first}'，剩余未分类商品数量: ${mutableUnclassifiedItems.size}")
                        }
                    }
                },
                onAllClassified = { isCancelled ->
                    // 所有商品都已分类完成
                    // 保存分类数据
                    ProductCategoryData.saveCategories(context)

                    // 检查是否还有未分类的商品
                    if (mutableUnclassifiedItems.isNotEmpty()) {
                        android.util.Log.d("ContentParser", "警告：对话框关闭时仍有 ${mutableUnclassifiedItems.size} 个未分类商品")

                        // 只有当用户点击取消按钮时，才自动将剩余未分类商品添加到"其他"区域
                        if (isCancelled) {
                            android.util.Log.d("ContentParser", "用户点击了取消按钮，自动将剩余未分类商品添加到'其他'区域")

                            // 将剩余未分类商品添加到"其他"区域
                            val otherIndex = areas.indexOfFirst { it.name == "其他" }
                            if (otherIndex >= 0) {
                                // 显示分类信息
                                Toast.makeText(context, "将剩余 ${mutableUnclassifiedItems.size} 个未分类商品添加到 '其他' 区域", Toast.LENGTH_SHORT).show()
                                android.util.Log.d("ContentParser", "将剩余 ${mutableUnclassifiedItems.size} 个未分类商品添加到 '其他' 区域")

                                // 创建副本以避免循环中修改集合
                                val itemsToProcess = mutableUnclassifiedItems.toList()
                                for (item in itemsToProcess) {
                                    // 将商品添加到"其他"区域
                                    if (!categorizedItems.containsKey(otherIndex)) {
                                        categorizedItems[otherIndex] = mutableListOf()
                                    }
                                    categorizedItems[otherIndex]?.add(item)

                                    // 将商品添加到"其他"区域的分类中
                                    ProductCategoryData.addItemToCategory(item.first, "其他", context)

                                    // 处理新添加的商品
                                    val newItems = mapOf<Int, List<Pair<String, String>>>(otherIndex to listOf(item))
                                    processCategorizedItems(newItems, areas, 1)

                                    // 从未分类商品列表中移除已分类的商品
                                    mutableUnclassifiedItems.remove(item)
                                }
                            }

                            Toast.makeText(context, context.getString(R.string.all_items_classified), Toast.LENGTH_SHORT).show()
                            android.util.Log.d("ContentParser", "所有商品已分类完成")

                            // 通知完成批量分类
                            onBatchClassifyComplete?.invoke()
                        } else {
                            // 如果不是用户点击取消按钮，则重新显示对话框
                            android.util.Log.d("ContentParser", "非取消操作，但仍有未分类商品，重新显示对话框")

                            // 重新创建并显示对话框
                            val newDialog = com.wanzijiejie.app.ui.components.BatchClassifyDialog3(
                                context = context,
                                areas = areas,
                                unclassifiedItems = mutableUnclassifiedItems.toList(),
                                onItemClassified = { itemName, selectedAreaIndex ->
                                    // 将商品添加到选定的区域
                                    if (selectedAreaIndex >= 0 && selectedAreaIndex < areas.size) {
                                        // 找到对应的商品和数量
                                        val item = mutableUnclassifiedItems.find { it.first == itemName }
                                        if (item != null) {
                                            // 打印分类信息，便于调试
                                            Toast.makeText(context, "将商品 '${item.first}' 添加到 '${areas[selectedAreaIndex].name}' 区域", Toast.LENGTH_SHORT).show()
                                            android.util.Log.d("ContentParser", "将商品 '${item.first}' 添加到 '${areas[selectedAreaIndex].name}' 区域")

                                            // 将商品添加到选定的区域
                                            if (!categorizedItems.containsKey(selectedAreaIndex)) {
                                                categorizedItems[selectedAreaIndex] = mutableListOf()
                                            }
                                            categorizedItems[selectedAreaIndex]?.add(item)

                                            // 将商品添加到选定区域的分类中
                                            val selectedAreaName = areas[selectedAreaIndex].name
                                            ProductCategoryData.addItemToCategory(itemName, selectedAreaName, context)

                                            // 处理新添加的商品
                                            val newItems = mapOf<Int, List<Pair<String, String>>>(selectedAreaIndex to listOf(item))
                                            processCategorizedItems(newItems, areas, 1)

                                            // 从未分类商品列表中移除已分类的商品
                                            mutableUnclassifiedItems.remove(item)
                                            android.util.Log.d("ContentParser", "从未分类商品列表中移除商品 '${item.first}'，剩余未分类商品数量: ${mutableUnclassifiedItems.size}")
                                        }
                                    }
                                },
                                onAllClassified = { isCancelled ->
                                    // 如果没有未分类的商品，则通知完成批量分类
                                    if (mutableUnclassifiedItems.isEmpty()) {
                                        Toast.makeText(context, context.getString(R.string.all_items_classified), Toast.LENGTH_SHORT).show()
                                        android.util.Log.d("ContentParser", "所有商品已分类完成")
                                        onBatchClassifyComplete?.invoke()
                                    } else if (isCancelled) {
                                        // 如果是取消操作，则将剩余未分类商品添加到"其他"区域
                                        val otherIndex = areas.indexOfFirst { it.name == "其他" }
                                        if (otherIndex >= 0) {
                                            // 显示分类信息
                                            Toast.makeText(context, "将剩余 ${mutableUnclassifiedItems.size} 个未分类商品添加到 '其他' 区域", Toast.LENGTH_SHORT).show()
                                            android.util.Log.d("ContentParser", "将剩余 ${mutableUnclassifiedItems.size} 个未分类商品添加到 '其他' 区域")

                                            // 创建副本以避免循环中修改集合
                                            val itemsToProcess = mutableUnclassifiedItems.toList()
                                            for (item in itemsToProcess) {
                                                // 将商品添加到"其他"区域
                                                if (!categorizedItems.containsKey(otherIndex)) {
                                                    categorizedItems[otherIndex] = mutableListOf()
                                                }
                                                categorizedItems[otherIndex]?.add(item)

                                                // 将商品添加到"其他"区域的分类中
                                                ProductCategoryData.addItemToCategory(item.first, "其他", context)

                                                // 处理新添加的商品
                                                val newItems = mapOf<Int, List<Pair<String, String>>>(otherIndex to listOf(item))
                                                processCategorizedItems(newItems, areas, 1)

                                                // 从未分类商品列表中移除已分类的商品
                                                mutableUnclassifiedItems.remove(item)
                                            }

                                            Toast.makeText(context, context.getString(R.string.all_items_classified), Toast.LENGTH_SHORT).show()
                                            android.util.Log.d("ContentParser", "所有商品已分类完成")
                                            onBatchClassifyComplete?.invoke()
                                        }
                                    } else {
                                        // 如果不是取消操作，则继续使用当前对话框
                                        android.util.Log.d("ContentParser", "非取消操作，但仍有未分类商品，继续使用当前对话框")

                                        // 直接显示当前对话框，不重新创建
                                        // 创建新的对话框实例，但使用相同的回调函数
                                        val batchClassifyDialog = com.wanzijiejie.app.ui.components.BatchClassifyDialog3(
                                            context = context,
                                            areas = areas,
                                            unclassifiedItems = mutableUnclassifiedItems.toList(),
                                            onItemClassified = { itemName, selectedAreaIndex ->
                                                // 将商品添加到选定的区域
                                                if (selectedAreaIndex >= 0 && selectedAreaIndex < areas.size) {
                                                    // 找到对应的商品和数量
                                                    val item = mutableUnclassifiedItems.find { it.first == itemName }
                                                    if (item != null) {
                                                        // 打印分类信息，便于调试
                                                        Toast.makeText(context, "将商品 '${item.first}' 添加到 '${areas[selectedAreaIndex].name}' 区域", Toast.LENGTH_SHORT).show()
                                                        android.util.Log.d("ContentParser", "将商品 '${item.first}' 添加到 '${areas[selectedAreaIndex].name}' 区域")

                                                        // 将商品添加到选定的区域
                                                        if (!categorizedItems.containsKey(selectedAreaIndex)) {
                                                            categorizedItems[selectedAreaIndex] = mutableListOf()
                                                        }
                                                        categorizedItems[selectedAreaIndex]?.add(item)

                                                        // 将商品添加到选定区域的分类中
                                                        val selectedAreaName = areas[selectedAreaIndex].name
                                                        ProductCategoryData.addItemToCategory(itemName, selectedAreaName, context)

                                                        // 处理新添加的商品
                                                        val newItems = mapOf<Int, List<Pair<String, String>>>(selectedAreaIndex to listOf(item))
                                                        processCategorizedItems(newItems, areas, 1)

                                                        // 从未分类商品列表中移除已分类的商品
                                                        mutableUnclassifiedItems.remove(item)
                                                        android.util.Log.d("ContentParser", "从未分类商品列表中移除商品 '${item.first}'，剩余未分类商品数量: ${mutableUnclassifiedItems.size}")
                                                    }
                                                }
                                            },
                                            onAllClassified = { innerIsCancelled ->
                                                // 如果没有未分类的商品，则通知完成批量分类
                                                if (mutableUnclassifiedItems.isEmpty()) {
                                                    Toast.makeText(context, context.getString(R.string.all_items_classified), Toast.LENGTH_SHORT).show()
                                                    android.util.Log.d("ContentParser", "所有商品已分类完成")
                                                    onBatchClassifyComplete?.invoke()
                                                } else if (innerIsCancelled) {
                                                    // 如果是取消操作，则将剩余未分类商品添加到"其他"区域
                                                    val otherIndex = areas.indexOfFirst { it.name == "其他" }
                                                    if (otherIndex >= 0) {
                                                        // 显示分类信息
                                                        Toast.makeText(context, "将剩余 ${mutableUnclassifiedItems.size} 个未分类商品添加到 '其他' 区域", Toast.LENGTH_SHORT).show()
                                                        android.util.Log.d("ContentParser", "将剩余 ${mutableUnclassifiedItems.size} 个未分类商品添加到 '其他' 区域")

                                                        // 创建副本以避免循环中修改集合
                                                        val itemsToProcess = mutableUnclassifiedItems.toList()
                                                        for (item in itemsToProcess) {
                                                            // 将商品添加到"其他"区域
                                                            if (!categorizedItems.containsKey(otherIndex)) {
                                                                categorizedItems[otherIndex] = mutableListOf()
                                                            }
                                                            categorizedItems[otherIndex]?.add(item)

                                                            // 将商品添加到"其他"区域的分类中
                                                            ProductCategoryData.addItemToCategory(item.first, "其他", context)

                                                            // 处理新添加的商品
                                                            val newItems = mapOf<Int, List<Pair<String, String>>>(otherIndex to listOf(item))
                                                            processCategorizedItems(newItems, areas, 1)

                                                            // 从未分类商品列表中移除已分类的商品
                                                            mutableUnclassifiedItems.remove(item)
                                                        }

                                                        Toast.makeText(context, context.getString(R.string.all_items_classified), Toast.LENGTH_SHORT).show()
                                                        android.util.Log.d("ContentParser", "所有商品已分类完成")
                                                        onBatchClassifyComplete?.invoke()
                                                    }
                                                } else {
                                                    // 如果不是取消操作，则继续使用当前对话框
                                                    android.util.Log.d("ContentParser", "非取消操作，但仍有未分类商品，继续使用当前对话框")

                                                    // 延迟一下再次显示对话框，避免界面闪烁
                                                    android.os.Handler(context.mainLooper).postDelayed({
                                                        // 创建新的对话框实例，但使用相同的回调函数
                                                        val newDialog = com.wanzijiejie.app.ui.components.BatchClassifyDialog3(
                                                            context = context,
                                                            areas = areas,
                                                            unclassifiedItems = mutableUnclassifiedItems.toList(),
                                                            onItemClassified = { itemName, selectedAreaIndex ->
                                                                // 将商品添加到选定的区域
                                                                if (selectedAreaIndex >= 0 && selectedAreaIndex < areas.size) {
                                                                    // 找到对应的商品和数量
                                                                    val item = mutableUnclassifiedItems.find { it.first == itemName }
                                                                    if (item != null) {
                                                                        // 打印分类信息，便于调试
                                                                        Toast.makeText(context, "将商品 '${item.first}' 添加到 '${areas[selectedAreaIndex].name}' 区域", Toast.LENGTH_SHORT).show()
                                                                        android.util.Log.d("ContentParser", "将商品 '${item.first}' 添加到 '${areas[selectedAreaIndex].name}' 区域")

                                                                        // 将商品添加到选定的区域
                                                                        if (!categorizedItems.containsKey(selectedAreaIndex)) {
                                                                            categorizedItems[selectedAreaIndex] = mutableListOf()
                                                                        }
                                                                        categorizedItems[selectedAreaIndex]?.add(item)

                                                                        // 将商品添加到选定区域的分类中
                                                                        val selectedAreaName = areas[selectedAreaIndex].name
                                                                        ProductCategoryData.addItemToCategory(itemName, selectedAreaName, context)

                                                                        // 处理新添加的商品
                                                                        val newItems = mapOf<Int, List<Pair<String, String>>>(selectedAreaIndex to listOf(item))
                                                                        processCategorizedItems(newItems, areas, 1)

                                                                        // 从未分类商品列表中移除已分类的商品
                                                                        mutableUnclassifiedItems.remove(item)
                                                                        android.util.Log.d("ContentParser", "从未分类商品列表中移除商品 '${item.first}'，剩余未分类商品数量: ${mutableUnclassifiedItems.size}")
                                                                    }
                                                                }
                                                            },
                                                            onAllClassified = { innerInnerIsCancelled ->
                                                                // 如果没有未分类的商品，则通知完成批量分类
                                                                if (mutableUnclassifiedItems.isEmpty()) {
                                                                    Toast.makeText(context, context.getString(R.string.all_items_classified), Toast.LENGTH_SHORT).show()
                                                                    android.util.Log.d("ContentParser", "所有商品已分类完成")
                                                                    onBatchClassifyComplete?.invoke()
                                                                } else if (innerInnerIsCancelled) {
                                                                    // 如果是取消操作，则将剩余未分类商品添加到"其他"区域
                                                                    val otherIndex = areas.indexOfFirst { it.name == "其他" }
                                                                    if (otherIndex >= 0) {
                                                                        // 显示分类信息
                                                                        Toast.makeText(context, "将剩余 ${mutableUnclassifiedItems.size} 个未分类商品添加到 '其他' 区域", Toast.LENGTH_SHORT).show()
                                                                        android.util.Log.d("ContentParser", "将剩余 ${mutableUnclassifiedItems.size} 个未分类商品添加到 '其他' 区域")

                                                                        // 创建副本以避免循环中修改集合
                                                                        val itemsToProcess = mutableUnclassifiedItems.toList()
                                                                        for (item in itemsToProcess) {
                                                                            // 将商品添加到"其他"区域
                                                                            if (!categorizedItems.containsKey(otherIndex)) {
                                                                                categorizedItems[otherIndex] = mutableListOf()
                                                                            }
                                                                            categorizedItems[otherIndex]?.add(item)

                                                                            // 将商品添加到"其他"区域的分类中
                                                                            ProductCategoryData.addItemToCategory(item.first, "其他", context)

                                                                            // 处理新添加的商品
                                                                            val newItems = mapOf<Int, List<Pair<String, String>>>(otherIndex to listOf(item))
                                                                            processCategorizedItems(newItems, areas, 1)

                                                                            // 从未分类商品列表中移除已分类的商品
                                                                            mutableUnclassifiedItems.remove(item)
                                                                        }

                                                                        Toast.makeText(context, context.getString(R.string.all_items_classified), Toast.LENGTH_SHORT).show()
                                                                        android.util.Log.d("ContentParser", "所有商品已分类完成")
                                                                        onBatchClassifyComplete?.invoke()
                                                                    }
                                                                } else {
                                                                    // 如果不是取消操作，则继续使用当前对话框
                                                                    android.util.Log.d("ContentParser", "非取消操作，但仍有未分类商品，继续使用当前对话框")

                                                                    // 延迟一下再次显示对话框，避免界面闪烁
                                                                    android.os.Handler(context.mainLooper).postDelayed({
                                                                        // 创建新的对话框实例，但使用相同的回调函数
                                                                        val newestDialog = com.wanzijiejie.app.ui.components.BatchClassifyDialog3(
                                                                            context = context,
                                                                            areas = areas,
                                                                            unclassifiedItems = mutableUnclassifiedItems.toList(),
                                                                            onItemClassified = { itemName, selectedAreaIndex ->
                                                                                // 将商品添加到选定的区域
                                                                                if (selectedAreaIndex >= 0 && selectedAreaIndex < areas.size) {
                                                                                    // 找到对应的商品和数量
                                                                                    val item = mutableUnclassifiedItems.find { it.first == itemName }
                                                                                    if (item != null) {
                                                                                        // 打印分类信息，便于调试
                                                                                        Toast.makeText(context, "将商品 '${item.first}' 添加到 '${areas[selectedAreaIndex].name}' 区域", Toast.LENGTH_SHORT).show()
                                                                                        android.util.Log.d("ContentParser", "将商品 '${item.first}' 添加到 '${areas[selectedAreaIndex].name}' 区域")

                                                                                        // 将商品添加到选定的区域
                                                                                        if (!categorizedItems.containsKey(selectedAreaIndex)) {
                                                                                            categorizedItems[selectedAreaIndex] = mutableListOf()
                                                                                        }
                                                                                        categorizedItems[selectedAreaIndex]?.add(item)

                                                                                        // 将商品添加到选定区域的分类中
                                                                                        val selectedAreaName = areas[selectedAreaIndex].name
                                                                                        ProductCategoryData.addItemToCategory(itemName, selectedAreaName, context)

                                                                                        // 处理新添加的商品
                                                                                        val newItems = mapOf<Int, List<Pair<String, String>>>(selectedAreaIndex to listOf(item))
                                                                                        processCategorizedItems(newItems, areas, 1)

                                                                                        // 从未分类商品列表中移除已分类的商品
                                                                                        mutableUnclassifiedItems.remove(item)
                                                                                        android.util.Log.d("ContentParser", "从未分类商品列表中移除商品 '${item.first}'，剩余未分类商品数量: ${mutableUnclassifiedItems.size}")
                                                                                    }
                                                                                }
                                                                            },
                                                                            onAllClassified = { finalIsCancelled ->
                                                                                // 如果没有未分类的商品，则通知完成批量分类
                                                                                if (mutableUnclassifiedItems.isEmpty()) {
                                                                                    Toast.makeText(context, context.getString(R.string.all_items_classified), Toast.LENGTH_SHORT).show()
                                                                                    android.util.Log.d("ContentParser", "所有商品已分类完成")
                                                                                    onBatchClassifyComplete?.invoke()
                                                                                } else if (finalIsCancelled) {
                                                                                    // 如果是取消操作，则将剩余未分类商品添加到"其他"区域
                                                                                    val otherIndex = areas.indexOfFirst { it.name == "其他" }
                                                                                    if (otherIndex >= 0) {
                                                                                        // 显示分类信息
                                                                                        Toast.makeText(context, "将剩余 ${mutableUnclassifiedItems.size} 个未分类商品添加到 '其他' 区域", Toast.LENGTH_SHORT).show()
                                                                                        android.util.Log.d("ContentParser", "将剩余 ${mutableUnclassifiedItems.size} 个未分类商品添加到 '其他' 区域")

                                                                                        // 创建副本以避免循环中修改集合
                                                                                        val itemsToProcess = mutableUnclassifiedItems.toList()
                                                                                        for (item in itemsToProcess) {
                                                                                            // 将商品添加到"其他"区域
                                                                                            if (!categorizedItems.containsKey(otherIndex)) {
                                                                                                categorizedItems[otherIndex] = mutableListOf()
                                                                                            }
                                                                                            categorizedItems[otherIndex]?.add(item)

                                                                                            // 将商品添加到"其他"区域的分类中
                                                                                            ProductCategoryData.addItemToCategory(item.first, "其他", context)

                                                                                            // 处理新添加的商品
                                                                                            val newItems = mapOf<Int, List<Pair<String, String>>>(otherIndex to listOf(item))
                                                                                            processCategorizedItems(newItems, areas, 1)

                                                                                            // 从未分类商品列表中移除已分类的商品
                                                                                            mutableUnclassifiedItems.remove(item)
                                                                                        }

                                                                                        Toast.makeText(context, context.getString(R.string.all_items_classified), Toast.LENGTH_SHORT).show()
                                                                                        android.util.Log.d("ContentParser", "所有商品已分类完成")
                                                                                        onBatchClassifyComplete?.invoke()
                                                                                    }
                                                                                } else {
                                                                                    // 如果不是取消操作，则继续使用当前对话框
                                                                                    android.util.Log.d("ContentParser", "非取消操作，但仍有未分类商品，继续使用当前对话框")

                                                                                    // 延迟一下再次显示对话框，避免界面闪烁
                                                                                    android.os.Handler(context.mainLooper).postDelayed({
                                                                                        // 创建新的对话框实例，但使用相同的回调函数
                                                                                        val finalDialog = com.wanzijiejie.app.ui.components.BatchClassifyDialog3(
                                                                                            context = context,
                                                                                            areas = areas,
                                                                                            unclassifiedItems = mutableUnclassifiedItems.toList(),
                                                                                            onItemClassified = { itemName, selectedAreaIndex ->
                                                                                                // 将商品添加到选定的区域
                                                                                                if (selectedAreaIndex >= 0 && selectedAreaIndex < areas.size) {
                                                                                                    // 找到对应的商品和数量
                                                                                                    val item = mutableUnclassifiedItems.find { it.first == itemName }
                                                                                                    if (item != null) {
                                                                                                        // 打印分类信息，便于调试
                                                                                                        Toast.makeText(context, "将商品 '${item.first}' 添加到 '${areas[selectedAreaIndex].name}' 区域", Toast.LENGTH_SHORT).show()
                                                                                                        android.util.Log.d("ContentParser", "将商品 '${item.first}' 添加到 '${areas[selectedAreaIndex].name}' 区域")

                                                                                                        // 将商品添加到选定的区域
                                                                                                        if (!categorizedItems.containsKey(selectedAreaIndex)) {
                                                                                                            categorizedItems[selectedAreaIndex] = mutableListOf()
                                                                                                        }
                                                                                                        categorizedItems[selectedAreaIndex]?.add(item)

                                                                                                        // 将商品添加到选定区域的分类中
                                                                                                        val selectedAreaName = areas[selectedAreaIndex].name
                                                                                                        ProductCategoryData.addItemToCategory(itemName, selectedAreaName, context)

                                                                                                        // 处理新添加的商品
                                                                                                        val newItems = mapOf<Int, List<Pair<String, String>>>(selectedAreaIndex to listOf(item))
                                                                                                        processCategorizedItems(newItems, areas, 1)

                                                                                                        // 从未分类商品列表中移除已分类的商品
                                                                                                        mutableUnclassifiedItems.remove(item)
                                                                                                        android.util.Log.d("ContentParser", "从未分类商品列表中移除商品 '${item.first}'，剩余未分类商品数量: ${mutableUnclassifiedItems.size}")
                                                                                                    }
                                                                                                }
                                                                                            },
                                                                                            onAllClassified = { ultimateIsCancelled ->
                                                                                                // 如果没有未分类的商品，则通知完成批量分类
                                                                                                if (mutableUnclassifiedItems.isEmpty()) {
                                                                                                    Toast.makeText(context, context.getString(R.string.all_items_classified), Toast.LENGTH_SHORT).show()
                                                                                                    android.util.Log.d("ContentParser", "所有商品已分类完成")
                                                                                                    onBatchClassifyComplete?.invoke()
                                                                                                } else if (ultimateIsCancelled) {
                                                                                                    // 如果是取消操作，则将剩余未分类商品添加到"其他"区域
                                                                                                    val otherIndex = areas.indexOfFirst { it.name == "其他" }
                                                                                                    if (otherIndex >= 0) {
                                                                                                        // 显示分类信息
                                                                                                        Toast.makeText(context, "将剩余 ${mutableUnclassifiedItems.size} 个未分类商品添加到 '其他' 区域", Toast.LENGTH_SHORT).show()
                                                                                                        android.util.Log.d("ContentParser", "将剩余 ${mutableUnclassifiedItems.size} 个未分类商品添加到 '其他' 区域")

                                                                                                        // 创建副本以避免循环中修改集合
                                                                                                        val itemsToProcess = mutableUnclassifiedItems.toList()
                                                                                                        for (item in itemsToProcess) {
                                                                                                            // 将商品添加到"其他"区域
                                                                                                            if (!categorizedItems.containsKey(otherIndex)) {
                                                                                                                categorizedItems[otherIndex] = mutableListOf()
                                                                                                            }
                                                                                                            categorizedItems[otherIndex]?.add(item)

                                                                                                            // 将商品添加到"其他"区域的分类中
                                                                                                            ProductCategoryData.addItemToCategory(item.first, "其他", context)

                                                                                                            // 处理新添加的商品
                                                                                                            val newItems = mapOf<Int, List<Pair<String, String>>>(otherIndex to listOf(item))
                                                                                                            processCategorizedItems(newItems, areas, 1)

                                                                                                            // 从未分类商品列表中移除已分类的商品
                                                                                                            mutableUnclassifiedItems.remove(item)
                                                                                                        }

                                                                                                        Toast.makeText(context, context.getString(R.string.all_items_classified), Toast.LENGTH_SHORT).show()
                                                                                                        android.util.Log.d("ContentParser", "所有商品已分类完成")
                                                                                                        onBatchClassifyComplete?.invoke()
                                                                                                    }
                                                                                                } else {
                                                                                                    // 如果不是取消操作，则继续使用当前对话框
                                                                                                    android.util.Log.d("ContentParser", "非取消操作，但仍有未分类商品，继续使用当前对话框")
                                                                                                }
                                                                                            }
                                                                                        )
                                                                                        finalDialog.show()
                                                                                    }, 300) // 延迟300毫秒
                                                                                }
                                                                            }
                                                                        )
                                                                        newestDialog.show()
                                                                    }, 300) // 延迟300毫秒
                                                                }
                                                            }
                                                        )
                                                        newDialog.show()
                                                    }, 300) // 延迟300毫秒
                                                }
                                            }
                                        )

                                        // 显示对话框
                                        batchClassifyDialog.show()
                                    }
                                }
                            )

                            // 显示新的对话框
                            newDialog.show()
                        }
                    } else {
                        // 如果没有未分类的商品，则通知完成批量分类
                        Toast.makeText(context, context.getString(R.string.all_items_classified), Toast.LENGTH_SHORT).show()
                        android.util.Log.d("ContentParser", "所有商品已分类完成")

                        // 通知完成批量分类
                        onBatchClassifyComplete?.invoke()
                    }
                }
            )

            // 显示对话框，并确保它不会被取消
            batchClassifyDialog.show()
            android.util.Log.d("ContentParser", "显示批量分类对话框，未分类商品数量: ${unclassifiedItems.size}")
        } else {
            // 如果没有未分类的商品，直接通知完成
            onBatchClassifyComplete?.invoke()
        }

        return true
    }

    /**
     * 处理分类后的商品
     * @param categorizedItems 分类后的商品
     * @param areas 区域列表
     * @param mode 处理模式：0=覆盖模式，1=继续添加模式
     */
    private fun processCategorizedItems(
        categorizedItems: Map<Int, List<Pair<String, String>>>,
        areas: MutableList<Area>,
        mode: Int = 0, // 0=覆盖模式，1=继续添加模式
        context: Context? = null // 添加上下文参数，用于显示调试信息
    ) {
        // 打印分类后的商品信息，便于调试
        if (context != null) {
            val totalItems = categorizedItems.values.sumOf { it.size }
            Toast.makeText(context, "分类后的商品数量: $totalItems", Toast.LENGTH_SHORT).show()
        }

        // 处理每个区域的商品
        categorizedItems.forEach { (areaIndex, items) ->
            // 获取当前区域的商品
            val currentProducts = areas[areaIndex].products

            // 如果是覆盖模式，先清空当前区域的商品
            if (mode == 0) {
                currentProducts.clear()
            }

            // 将新商品按名称长度从短到长排序
            val sortedItems = items.sortedBy { it.first.length }

            // 存储已添加的商品名称和对应的行号、位置，用于检查重复和更新
            val existingProductsMap = mutableMapOf<String, Pair<Int, Int>>() // 商品名 -> (rowIndex, position)

            // 添加当前区域已有的商品到集合中，用于检查重复
            currentProducts.forEachIndexed { rowIndex, product ->
                if (product.name1.isNotEmpty()) existingProductsMap[product.name1] = Pair(rowIndex, 1) // 1表示第一个位置
                if (product.name2.isNotEmpty()) existingProductsMap[product.name2] = Pair(rowIndex, 2) // 2表示第二个位置
            }

            // 如果是覆盖模式，或者没有商品，直接处理所有商品
            if (mode == 0 || currentProducts.isEmpty()) {
                // 将所有商品添加到当前区域
                var index = 0
                while (index < sortedItems.size) {
                    val (name1, quantity1) = sortedItems[index]
                    index++

                    if (index < sortedItems.size) {
                        // 如果还有下一个商品，则一行放两个
                        val (name2, quantity2) = sortedItems[index]
                        currentProducts.add(Product(name1 = name1, quantity1 = quantity1, name2 = name2, quantity2 = quantity2))
                        index++
                    } else {
                        // 如果没有下一个商品，则只放一个
                        currentProducts.add(Product(name1 = name1, quantity1 = quantity1))
                    }
                }
                return@forEach
            }

            // 如果是继续添加模式，先处理重复的商品（更新已存在的商品）
            val updatedItems = mutableListOf<Pair<String, String>>()
            val newItems = mutableListOf<Pair<String, String>>()

            sortedItems.forEach { (name, quantity) ->
                if (existingProductsMap.containsKey(name)) {
                    // 如果商品已存在，更新其数量
                    val (rowIndex, position) = existingProductsMap[name]!!
                    val product = currentProducts[rowIndex]
                    if (position == 1) {
                        // 更新第一个位置的商品
                        currentProducts[rowIndex] = product.copy(quantity1 = quantity)
                    } else {
                        // 更新第二个位置的商品
                        currentProducts[rowIndex] = product.copy(quantity2 = quantity)
                    }
                    updatedItems.add(Pair(name, quantity))
                } else {
                    // 如果商品不存在，添加到新商品列表
                    newItems.add(Pair(name, quantity))
                }
            }

            // 如果没有新商品，直接返回
            if (newItems.isEmpty()) return@forEach

            // 将新商品添加到当前区域
            var index = 0

            // 首先检查是否有完全空的行（两个位置都为空）
            val emptyRows = mutableListOf<Int>()
            for (i in 0 until currentProducts.size) {
                val product = currentProducts[i]
                if (product.name1.isEmpty() && product.name2.isEmpty()) {
                    emptyRows.add(i)
                }
            }

            // 先填充完全空的行
            for (rowIndex in emptyRows) {
                if (index < newItems.size) {
                    val (name1, quantity1) = newItems[index]
                    index++

                    if (index < newItems.size) {
                        // 如果还有下一个商品，则一行放两个
                        val (name2, quantity2) = newItems[index]
                        currentProducts[rowIndex] = Product(name1 = name1, quantity1 = quantity1, name2 = name2, quantity2 = quantity2)
                        index++
                    } else {
                        // 如果没有下一个商品，则只放一个
                        currentProducts[rowIndex] = Product(name1 = name1, quantity1 = quantity1)
                    }
                }
            }

            // 然后填充部分空的行（只有一个位置为空）
            if (index < newItems.size) {
                for (i in 0 until currentProducts.size) {
                    val product = currentProducts[i]

                    // 如果第一个位置为空，第二个不为空
                    if (product.name1.isEmpty() && product.name2.isNotEmpty() && index < newItems.size) {
                        val (name, quantity) = newItems[index]
                        currentProducts[i] = product.copy(name1 = name, quantity1 = quantity)
                        index++
                    }

                    // 如果第二个位置为空，第一个不为空
                    if (product.name2.isEmpty() && product.name1.isNotEmpty() && index < newItems.size) {
                        val (name, quantity) = newItems[index]
                        currentProducts[i] = product.copy(name2 = name, quantity2 = quantity)
                        index++
                    }

                    // 如果所有商品都已添加，则退出循环
                    if (index >= newItems.size) break
                }
            }

            // 如果还有剩余商品，创建新行
            while (index < newItems.size) {
                val (name1, quantity1) = newItems[index]
                index++

                // 如果还有下一个商品，则一行放两个
                if (index < newItems.size) {
                    val (name2, quantity2) = newItems[index]
                    currentProducts.add(Product(name1 = name1, quantity1 = quantity1, name2 = name2, quantity2 = quantity2))
                    index++
                } else {
                    // 如果没有下一个商品，则只放一个
                    currentProducts.add(Product(name1 = name1, quantity1 = quantity1))
                }
            }
        }
    }
}
