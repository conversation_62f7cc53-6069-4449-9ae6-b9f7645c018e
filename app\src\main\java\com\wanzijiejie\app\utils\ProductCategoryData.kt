package com.wanzijiejie.app.utils

import android.content.Context
import android.content.SharedPreferences
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * 商品分类数据
 * 用于存储和管理商品分类数据
 */
object ProductCategoryData {
    // 分类数据
    private val categoryData = mutableMapOf<String, MutableList<String>>()
    
    // 是否已加载
    private var isLoaded = false
    
    /**
     * 加载分类数据
     * 
     * @param context 上下文
     */
    fun loadCategories(context: Context) {
        if (isLoaded) {
            return
        }
        
        // 获取SharedPreferences
        val sharedPreferences = context.getSharedPreferences("product_categories", Context.MODE_PRIVATE)
        
        // 加载分类数据
        categoryData.clear()
        
        // 加载饭堂分类
        loadCategory(sharedPreferences, "饭堂")
        
        // 加载麻辣分类
        loadCategory(sharedPreferences, "麻辣")
        
        // 加载冻货分类
        loadCategory(sharedPreferences, "冻货")
        
        // 加载其他分类
        loadCategory(sharedPreferences, "其他")
        
        // 标记为已加载
        isLoaded = true
    }
    
    /**
     * 加载分类
     * 
     * @param sharedPreferences SharedPreferences
     * @param categoryName 分类名称
     */
    private fun loadCategory(sharedPreferences: SharedPreferences, categoryName: String) {
        // 获取分类数据
        val categoryJson = sharedPreferences.getString(categoryName, "[]")
        
        // 解析分类数据
        val type = object : TypeToken<List<String>>() {}.type
        val items = Gson().fromJson<List<String>>(categoryJson, type) ?: emptyList()
        
        // 添加到分类数据中
        categoryData[categoryName] = items.toMutableList()
    }
    
    /**
     * 保存分类数据
     * 
     * @param context 上下文
     */
    fun saveCategories(context: Context) {
        // 获取SharedPreferences
        val sharedPreferences = context.getSharedPreferences("product_categories", Context.MODE_PRIVATE)
        
        // 获取编辑器
        val editor = sharedPreferences.edit()
        
        // 保存分类数据
        for ((categoryName, items) in categoryData) {
            // 转换为JSON
            val categoryJson = Gson().toJson(items)
            
            // 保存到SharedPreferences
            editor.putString(categoryName, categoryJson)
        }
        
        // 提交编辑
        editor.apply()
    }
    
    /**
     * 添加商品到分类
     * 
     * @param itemName 商品名称
     * @param categoryName 分类名称
     * @param context 上下文
     */
    fun addItemToCategory(itemName: String, categoryName: String, context: Context) {
        // 加载分类数据
        loadCategories(context)
        
        // 获取分类
        val category = categoryData[categoryName] ?: mutableListOf()
        
        // 如果商品不在分类中，则添加
        if (!category.contains(itemName)) {
            category.add(itemName)
            categoryData[categoryName] = category
            
            // 保存分类数据
            saveCategories(context)
        }
    }
    
    /**
     * 查找商品所属的分类
     * 
     * @param itemName 商品名称
     * @return 分类名称，如果未找到则返回null
     */
    fun findCategoryForItem(itemName: String): String? {
        // 遍历所有分类
        for ((categoryName, items) in categoryData) {
            // 如果商品在分类中，则返回分类名称
            if (items.contains(itemName)) {
                return categoryName
            }
        }
        
        // 未找到分类
        return null
    }
    
    /**
     * 获取分类中的所有商品
     * 
     * @param categoryName 分类名称
     * @return 商品列表
     */
    fun getItemsInCategory(categoryName: String): List<String> {
        // 获取分类
        return categoryData[categoryName]?.toList() ?: emptyList()
    }
    
    /**
     * 获取所有分类
     * 
     * @return 分类列表
     */
    fun getAllCategories(): List<String> {
        return categoryData.keys.toList()
    }
    
    /**
     * 清空分类数据
     * 
     * @param context 上下文
     */
    fun clearCategories(context: Context) {
        // 清空分类数据
        categoryData.clear()
        
        // 标记为未加载
        isLoaded = false
        
        // 获取SharedPreferences
        val sharedPreferences = context.getSharedPreferences("product_categories", Context.MODE_PRIVATE)
        
        // 获取编辑器
        val editor = sharedPreferences.edit()
        
        // 清空SharedPreferences
        editor.clear()
        
        // 提交编辑
        editor.apply()
    }
}
