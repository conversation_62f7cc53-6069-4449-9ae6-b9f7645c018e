package com.wanzijiejie.app.ui.screens

import android.app.DatePickerDialog
import android.content.ClipboardManager
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.widget.DatePicker
import android.widget.Toast
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.List
import androidx.compose.material.icons.filled.ShoppingCart
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.wanzijiejie.app.R
import com.wanzijiejie.app.services.MusicPlayerService
import com.wanzijiejie.app.ui.navigation.AppDestinations
import com.wanzijiejie.app.ui.theme.丸子姐姐报表Theme
import com.wanzijiejie.app.ui.screens.AutoOrderHelper
import java.util.Calendar
import java.util.Date

@Composable
fun HuangJiaShanScreen(
    navController: NavController = rememberNavController(),
    modifier: Modifier = Modifier,
    musicPlayerService: MusicPlayerService? = null
) {
    // 记住选择的日期
    var selectedDate by remember { mutableStateOf(Calendar.getInstance()) }

    // 获取当前上下文
    val context = LocalContext.current

    // 自动点击自动报货按钮的标志
    val shouldAutoClickOrderButton = remember { mutableStateOf(AutoOrderHelper.autoClickOrderButton) }

    // 如果需要自动点击自动报货按钮，则在页面加载后执行
    LaunchedEffect(shouldAutoClickOrderButton.value) {
        if (shouldAutoClickOrderButton.value) {
            // 延迟500毫秒，确保页面完全加载
            delay(500)

            // 获取明天的日期
            val tomorrow = Calendar.getInstance()
            tomorrow.add(Calendar.DAY_OF_MONTH, 1) // 增加1天
            val tomorrowMillis = tomorrow.timeInMillis

            // 跳转到明天的报表页面
            navController.navigate(AppDestinations.createReportRoute(tomorrowMillis)) {
                popUpTo(AppDestinations.HUANG_JIA_SHAN_ROUTE) { inclusive = false }
            }

            // 尝试从剪贴板获取内容
            val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clipData = clipboardManager.primaryClip

            // 如果剪贴板有内容，则将其粘贴到一键粘贴区域
            if (clipData != null && clipData.itemCount > 0) {
                val clipText = clipData.getItemAt(0).text.toString()

                // 设置AutoOrderHelper的值，以便在ReportScreen页面加载后自动粘贴
                AutoOrderHelper.clipboardContent = clipText
                AutoOrderHelper.needAutoPaste = true

                // 显示提示信息
                Toast.makeText(context, context.getString(R.string.auto_order_processing), Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(context, context.getString(R.string.clipboard_empty), Toast.LENGTH_SHORT).show()
            }

            // 重置自动点击标志，避免再次进入页面时自动点击
            AutoOrderHelper.autoClickOrderButton = false
            shouldAutoClickOrderButton.value = false
        }
    }

    // 创建日期选择器对话框
    val datePickerDialog = DatePickerDialog(
        context,
        { _: DatePicker, year: Int, month: Int, dayOfMonth: Int ->
            val calendar = Calendar.getInstance()
            calendar.set(year, month, dayOfMonth)
            selectedDate = calendar
        },
        selectedDate.get(Calendar.YEAR),
        selectedDate.get(Calendar.MONTH),
        selectedDate.get(Calendar.DAY_OF_MONTH)
    )

    Surface(
        modifier = modifier.fillMaxSize(),
        color = MaterialTheme.colorScheme.background
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = stringResource(R.string.huang_jia_shan),
                style = MaterialTheme.typography.headlineLarge,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(16.dp))

            // 日期选择和报表生成区域
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = stringResource(R.string.select_date),
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    // 显示选择的日期
                    Text(
                        text = "${selectedDate.get(Calendar.YEAR)}年${selectedDate.get(Calendar.MONTH) + 1}月${selectedDate.get(Calendar.DAY_OF_MONTH)}日",
                        style = MaterialTheme.typography.bodyLarge,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Row {
                        // 选择日期按钮
                        OutlinedButton(
                            onClick = { datePickerDialog.show() },
                            modifier = Modifier.weight(1f)
                        ) {
                            Text(stringResource(R.string.select_date))
                        }

                        Spacer(modifier = Modifier.width(16.dp))

                        // 生成报表按钮
                        Button(
                            onClick = {
                                // 导航到报表预览页面
                                navController.navigate("report/${selectedDate.timeInMillis}")
                            },
                            modifier = Modifier.weight(1f)
                        ) {
                            Text(stringResource(R.string.generate_report))
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // 自动报货按钮
                    Button(
                        onClick = {
                            // 获取明天的日期
                            val tomorrow = Calendar.getInstance()
                            tomorrow.add(Calendar.DAY_OF_MONTH, 1) // 增加1天
                            val tomorrowMillis = tomorrow.timeInMillis

                            // 跳转到明天的报表页面
                            navController.navigate(AppDestinations.createReportRoute(tomorrowMillis)) {
                                popUpTo(AppDestinations.HUANG_JIA_SHAN_ROUTE) { inclusive = false }
                            }

                            // 尝试从剪贴板获取内容
                            val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                            val clipData = clipboardManager.primaryClip

                            // 如果剪贴板有内容，则将其粘贴到一键粘贴区域
                            if (clipData != null && clipData.itemCount > 0) {
                                val clipText = clipData.getItemAt(0).text.toString()

                                // 设置AutoOrderHelper的值，以便在ReportScreen页面加载后自动粘贴
                                AutoOrderHelper.clipboardContent = clipText
                                AutoOrderHelper.needAutoPaste = true

                                // 显示提示信息
                                Toast.makeText(context, context.getString(R.string.auto_order_processing), Toast.LENGTH_SHORT).show()
                            } else {
                                Toast.makeText(context, context.getString(R.string.clipboard_empty), Toast.LENGTH_SHORT).show()
                            }
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Row {
                            Icon(
                                imageVector = Icons.Default.ShoppingCart,
                                contentDescription = stringResource(R.string.auto_order)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(stringResource(R.string.auto_order))
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // 区域分类管理按钮
                    Button(
                        onClick = {
                            navController.navigate(AppDestinations.CATEGORY_MANAGEMENT_ROUTE)
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Row {
                            Icon(
                                imageVector = Icons.Default.List,
                                contentDescription = stringResource(R.string.category_management)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(stringResource(R.string.category_management))
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // 历史记录按钮
                    Button(
                        onClick = {
                            navController.navigate(AppDestinations.HISTORY_ROUTE)
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Row {
                            Icon(
                                imageVector = Icons.Default.DateRange,
                                contentDescription = stringResource(R.string.history_records)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(stringResource(R.string.history_records))
                        }
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    // 自动报货设置
                    Card(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = "设置",
                                style = MaterialTheme.typography.titleLarge,
                                fontWeight = FontWeight.Bold
                            )

                            Spacer(modifier = Modifier.height(16.dp))

                            // 启动时自动报货开关
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Column(modifier = Modifier.weight(1f)) {
                                    Text(
                                        text = stringResource(R.string.auto_order_on_startup),
                                        style = MaterialTheme.typography.bodyLarge,
                                        fontWeight = FontWeight.Medium
                                    )
                                    Text(
                                        text = stringResource(R.string.auto_order_on_startup_desc),
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                                    )
                                }

                                // 读取当前设置
                                var autoOrderOnStartup by remember {
                                    mutableStateOf(AutoOrderHelper.getAutoOrderOnStartup(context))
                                }

                                Switch(
                                    checked = autoOrderOnStartup,
                                    onCheckedChange = { isChecked ->
                                        // 更新状态
                                        autoOrderOnStartup = isChecked

                                        // 保存设置
                                        AutoOrderHelper.setAutoOrderOnStartup(context, isChecked)

                                        // 显示提示
                                        Toast.makeText(context, context.getString(R.string.settings_saved), Toast.LENGTH_SHORT).show()
                                    }
                                )
                            }

                            Spacer(modifier = Modifier.height(16.dp))

                            // 背景音乐开关
                            musicPlayerService?.let { musicService ->
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    verticalAlignment = Alignment.CenterVertically,
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Column(modifier = Modifier.weight(1f)) {
                                        Text(
                                            text = stringResource(R.string.background_music),
                                            style = MaterialTheme.typography.bodyLarge,
                                            fontWeight = FontWeight.Medium
                                        )
                                        Text(
                                            text = stringResource(R.string.background_music_desc),
                                            style = MaterialTheme.typography.bodySmall,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                                        )
                                    }

                                    // 使用音乐服务的状态
                                    var musicEnabled by remember {
                                        mutableStateOf(musicService.isMusicEnabled.value)
                                    }

                                    Switch(
                                        checked = musicEnabled,
                                        onCheckedChange = { isChecked ->
                                            // 更新状态
                                            musicEnabled = isChecked

                                            // 保存设置并控制音乐播放
                                            musicService.saveMusicEnabledState(isChecked)

                                            // 显示提示
                                            Toast.makeText(context, context.getString(R.string.settings_saved), Toast.LENGTH_SHORT).show()
                                        }
                                    )
                                }
                            }
                        }
                    }
                }
            }

            // 简介和特色景点信息已移除
        }
    }
}

@Preview(showBackground = true)
@Composable
fun HuangJiaShanScreenPreview() {
    丸子姐姐报表Theme {
        HuangJiaShanScreen(
            navController = rememberNavController(),
            musicPlayerService = null // 预览时不需要音乐服务
        )
    }
}
