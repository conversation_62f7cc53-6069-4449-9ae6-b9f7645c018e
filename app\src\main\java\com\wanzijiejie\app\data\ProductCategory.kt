package com.wanzijiejie.app.data

import android.content.Context
import android.content.SharedPreferences
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * 商品分类数据类
 */
data class ProductCategory(
    val name: String,
    val areaName: String
)

/**
 * 商品分类管理器
 */
class ProductCategoryManager(context: Context) {
    private val sharedPreferences: SharedPreferences = context.getSharedPreferences(
        "product_categories", Context.MODE_PRIVATE
    )
    private val gson = Gson()
    
    /**
     * 获取所有商品分类
     */
    fun getAllCategories(): List<ProductCategory> {
        val categoriesJson = sharedPreferences.getString("categories", null) ?: return emptyList()
        val type = object : TypeToken<List<ProductCategory>>() {}.type
        return gson.fromJson(categoriesJson, type)
    }
    
    /**
     * 添加商品分类
     */
    fun addCategory(productName: String, areaName: String) {
        val categories = getAllCategories().toMutableList()
        
        // 检查是否已存在该商品分类
        val existingCategory = categories.find { it.name == productName }
        if (existingCategory != null) {
            // 如果已存在，则更新区域名称
            categories.remove(existingCategory)
            categories.add(ProductCategory(productName, areaName))
        } else {
            // 如果不存在，则添加新分类
            categories.add(ProductCategory(productName, areaName))
        }
        
        // 保存更新后的分类列表
        saveCategories(categories)
    }
    
    /**
     * 批量添加商品分类
     */
    fun addCategories(products: List<String>, areaName: String) {
        val categories = getAllCategories().toMutableList()
        
        // 添加新分类
        products.forEach { productName ->
            // 检查是否已存在该商品分类
            val existingCategory = categories.find { it.name == productName }
            if (existingCategory != null) {
                // 如果已存在，则更新区域名称
                categories.remove(existingCategory)
                categories.add(ProductCategory(productName, areaName))
            } else {
                // 如果不存在，则添加新分类
                categories.add(ProductCategory(productName, areaName))
            }
        }
        
        // 保存更新后的分类列表
        saveCategories(categories)
    }
    
    /**
     * 根据商品名称获取区域名称
     */
    fun getAreaNameByProduct(productName: String): String? {
        return getAllCategories().find { it.name == productName }?.areaName
    }
    
    /**
     * 保存分类列表
     */
    private fun saveCategories(categories: List<ProductCategory>) {
        val categoriesJson = gson.toJson(categories)
        sharedPreferences.edit().putString("categories", categoriesJson).apply()
    }
}
