package com.wanzijiejie.app.ui.components

import android.app.Dialog
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.Button
import android.widget.ListView
import android.widget.Spinner
import android.widget.TextView
import android.widget.Toast
import com.wanzijiejie.app.R
import com.wanzijiejie.app.ui.screens.Area

/**
 * 批量分类对话框
 * 用于批量分类未分类的商品
 *
 * 注意：这个对话框会一直显示，直到所有未分类商品都被分类完毕，或者用户点击取消按钮
 * 取消按钮会将所有剩余未分类商品自动分配到"其他"区域
 */
class BatchClassifyDialog3(
    private val context: Context,
    private val areas: List<Area>,
    private val unclassifiedItems: List<Pair<String, String>>,
    private val onItemClassified: (String, Int) -> Unit,
    private val onAllClassified: (Boolean) -> Unit
) {
    // 对话框
    private var dialog: Dialog = Dialog(context)

    // 剩余未分类商品
    private val remainingItems = unclassifiedItems.map { it.first }.toMutableList()

    // 商品名称到商品对象的映射，用于查找商品数量
    private val itemsMap = unclassifiedItems.associateBy({ it.first }, { it.second })

    // 记录是否已经处理过取消按钮的点击事件
    private var isCancelButtonClicked = false

    // 商品列表适配器
    private lateinit var itemsAdapter: ArrayAdapter<String>

    // 商品列表视图
    private lateinit var itemsListView: ListView

    // 区域选择器
    private lateinit var areaSpinner: Spinner

    // 确认按钮
    private lateinit var confirmButton: Button

    // 取消按钮
    private lateinit var cancelButton: Button

    init {
        // 创建对话框
        createDialog()
    }

    /**
     * 创建对话框
     * 注意：该方法只会在初始化时调用一次，确保对话框始终处于打开状态
     */
    private fun createDialog() {
        // 设置对话框标题
        dialog.setTitle(context.getString(R.string.unclassified_items_remaining, remainingItems.size))

        // 设置对话框不可取消
        dialog.setCancelable(false)

        // 创建对话框视图
        val view = LayoutInflater.from(context).inflate(R.layout.dialog_batch_classify, null)

        // 获取商品列表视图
        itemsListView = view.findViewById(R.id.itemsListView)

        // 获取区域选择器
        areaSpinner = view.findViewById(R.id.areaSpinner)

        // 获取确认按钮
        confirmButton = view.findViewById(R.id.confirmButton)

        // 获取取消按钮
        cancelButton = view.findViewById(R.id.cancelButton)

        // 创建商品列表适配器
        itemsAdapter = ArrayAdapter(context, android.R.layout.simple_list_item_multiple_choice, remainingItems)

        // 设置商品列表适配器
        itemsListView.adapter = itemsAdapter

        // 设置商品列表可多选
        itemsListView.choiceMode = ListView.CHOICE_MODE_MULTIPLE

        // 创建区域列表适配器
        val areaAdapter = ArrayAdapter(context, android.R.layout.simple_spinner_item, areas.map { it.name })

        // 设置区域列表下拉样式
        areaAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)

        // 设置区域列表适配器
        areaSpinner.adapter = areaAdapter

        // 设置区域列表默认选择"其他"
        val otherIndex = areas.indexOfFirst { it.name == "其他" }
        if (otherIndex >= 0) {
            areaSpinner.setSelection(otherIndex)
        }

        // 打印日志，确认对话框已创建
        android.util.Log.d("BatchClassifyDialog", "对话框已重新创建，剩余未分类商品数量: ${remainingItems.size}")

        // 设置对话框视图
        dialog.setContentView(view)

        // 设置确认按钮点击事件
        setupConfirmButtonClickListener()

        // 设置取消按钮点击事件
        setupCancelButtonClickListener()
    }

    /**
     * 设置确认按钮点击事件
     */
    private fun setupConfirmButtonClickListener() {
        confirmButton.setOnClickListener {
            // 打印日志，显示当前剩余未分类商品数量
            android.util.Log.d("BatchClassifyDialog", "确认按钮点击，当前剩余未分类商品数量: ${remainingItems.size}")

            val selectedAreaIndex = areaSpinner.selectedItemPosition

            // 获取选中的商品
            val selectedItems = mutableListOf<String>()
            for (i in 0 until itemsListView.count) {
                if (itemsListView.isItemChecked(i)) {
                    selectedItems.add(remainingItems[i])
                }
            }

            // 打印日志，显示选中的商品数量
            android.util.Log.d("BatchClassifyDialog", "选中的商品数量: ${selectedItems.size}")

            // 如果没有选中商品，提示用户选择商品
            if (selectedItems.isEmpty() && remainingItems.isNotEmpty()) {
                Toast.makeText(context, "请选择至少一个商品进行分类", Toast.LENGTH_SHORT).show()
                android.util.Log.d("BatchClassifyDialog", "未选中商品，提示用户选择")
                return@setOnClickListener  // 直接返回，不执行后续操作
            }

            // 创建一个副本，避免在循环中修改集合
            val itemsToProcess = selectedItems.toList()
            android.util.Log.d("BatchClassifyDialog", "开始处理选中的商品，数量: ${itemsToProcess.size}")

            // 对每个选中的商品进行分类
            for (item in itemsToProcess) {
                // 获取商品数量
                val quantity = itemsMap[item] ?: ""

                // 回调通知分类结果，传递商品名称和选择的区域索引
                onItemClassified(item, selectedAreaIndex)

                // 从剩余列表中移除已分类的商品
                remainingItems.remove(item)

                // 打印日志，确认商品已分类
                android.util.Log.d("BatchClassifyDialog", "商品 '$item' 已分类到 '${areas[selectedAreaIndex].name}' 区域，剩余未分类商品数量: ${remainingItems.size}")
            }

            // 打印当前剩余商品数量
            android.util.Log.d("BatchClassifyDialog", "处理完选中商品后，剩余未分类商品数量: ${remainingItems.size}")

            // 更新商品列表
            itemsAdapter.clear()
            itemsAdapter.addAll(remainingItems)
            itemsAdapter.notifyDataSetChanged()

            // 检查是否还有剩余商品
            if (remainingItems.isEmpty()) {
                // 关闭对话框前显示消息
                Toast.makeText(context, context.getString(R.string.all_items_classified), Toast.LENGTH_SHORT).show()
                android.util.Log.d("BatchClassifyDialog", "所有商品已分类完成，准备关闭对话框")

                // 延迟一下关闭对话框，让用户看到消息
                android.os.Handler(context.mainLooper).postDelayed({
                    // 关闭对话框
                    if (dialog.isShowing) {
                        android.util.Log.d("BatchClassifyDialog", "关闭对话框")
                        dialog.dismiss()
                        // 通知所有商品已分类完成，参数false表示不是取消操作
                        onAllClassified(false)
                    }
                }, 1000) // 延迟1000毫秒，增加延迟时间
            } else {
                // 清除所有选中状态，准备下一轮选择
                for (i in 0 until itemsListView.count) {
                    itemsListView.setItemChecked(i, false)
                }

                // 更新对话框标题，显示剩余未分类商品数量
                dialog.setTitle(context.getString(R.string.unclassified_items_remaining, remainingItems.size))

                // 显示成功分类消息
                Toast.makeText(context, "选中的商品已成功分配到指定区域，还有 ${remainingItems.size} 个未分类商品", Toast.LENGTH_SHORT).show()
                android.util.Log.d("BatchClassifyDialog", "选中的商品已成功分配到指定区域，还有 ${remainingItems.size} 个未分类商品")

                // 确保对话框不可取消
                dialog.setCancelable(false)

                // 添加日志以确认对话框保持打开状态
                android.util.Log.d("BatchClassifyDialog", "对话框保持打开状态，剩余未分类商品数量: ${remainingItems.size}")
            }
        }
    }

    /**
     * 设置取消按钮点击事件
     */
    private fun setupCancelButtonClickListener() {
        cancelButton.setOnClickListener {
            // 防止重复点击
            if (isCancelButtonClicked) {
                android.util.Log.d("BatchClassifyDialog", "取消按钮已经点击过，忽略重复点击")
                return@setOnClickListener
            }

            // 标记取消按钮已经点击
            isCancelButtonClicked = true

            // 将所有未分类的商品添加到"其他"区域
            if (remainingItems.isNotEmpty()) {
                val otherIndex = areas.indexOfFirst { it.name == "其他" }
                if (otherIndex >= 0) {
                    // 显示分类信息
                    Toast.makeText(context, "将剩余 ${remainingItems.size} 个未分类商品添加到 '其他' 区域", Toast.LENGTH_SHORT).show()
                    android.util.Log.d("BatchClassifyDialog", "将剩余 ${remainingItems.size} 个未分类商品添加到 '其他' 区域")

                    // 创建副本以避免循环中修改集合
                    val itemsToProcess = remainingItems.toList()
                    for (item in itemsToProcess) {
                        // 获取商品数量
                        val quantity = itemsMap[item] ?: ""

                        // 回调通知分类结果
                        onItemClassified(item, otherIndex)

                        // 从剩余列表中移除已分类的商品
                        remainingItems.remove(item)

                        // 打印日志，确认商品已分类
                        android.util.Log.d("BatchClassifyDialog", "商品 '$item' 已分类到 '其他' 区域，剩余未分类商品数量: ${remainingItems.size}")
                    }
                }
            }

            // 更新商品列表
            itemsAdapter.clear()
            itemsAdapter.addAll(remainingItems)
            itemsAdapter.notifyDataSetChanged()

            // 关闭对话框前显示消息
            Toast.makeText(context, context.getString(R.string.all_items_classified), Toast.LENGTH_SHORT).show()
            android.util.Log.d("BatchClassifyDialog", "所有商品已分类完成，准备关闭对话框")

            // 延迟一下关闭对话框，让用户看到消息
            android.os.Handler(context.mainLooper).postDelayed({
                // 关闭对话框
                if (dialog.isShowing) {
                    android.util.Log.d("BatchClassifyDialog", "关闭对话框")
                    dialog.dismiss()
                    // 通知所有商品已分类完成，参数true表示是取消操作
                    onAllClassified(true)
                }
            }, 1000) // 延迟1000毫秒，增加延迟时间
        }
    }

    /**
     * 显示对话框
     */
    fun show() {
        // 检查是否有未分类商品
        if (remainingItems.isEmpty()) {
            // 如果没有未分类商品，直接调用完成回调并返回
            android.util.Log.d("BatchClassifyDialog", "没有未分类商品，直接调用完成回调")
            onAllClassified(false)
            return
        }

        // 打印日志，显示未分类商品数量
        android.util.Log.d("BatchClassifyDialog", "显示对话框，未分类商品数量: ${remainingItems.size}")

        // 重置取消按钮点击标记
        isCancelButtonClicked = false

        // 如果对话框已经显示，则只更新列表，不重新创建对话框
        if (!dialog.isShowing) {
            dialog.show()
            android.util.Log.d("BatchClassifyDialog", "对话框已显示")
        } else {
            // 更新商品列表
            itemsAdapter.clear()
            itemsAdapter.addAll(remainingItems)
            itemsAdapter.notifyDataSetChanged()
            android.util.Log.d("BatchClassifyDialog", "对话框已经在显示中，更新商品列表")
        }

        // 确保对话框不可取消
        dialog.setCancelable(false)

        // 更新对话框标题，显示剩余未分类商品数量
        dialog.setTitle(context.getString(R.string.unclassified_items_remaining, remainingItems.size))
    }
}
