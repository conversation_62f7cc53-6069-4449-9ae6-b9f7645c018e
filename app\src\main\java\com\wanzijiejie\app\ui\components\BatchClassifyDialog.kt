package com.wanzijiejie.app.ui.components

import android.app.AlertDialog
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.ArrayAdapter
import android.widget.Button
import android.widget.ListView
import android.widget.Spinner
import android.widget.TextView
import android.widget.Toast
import com.wanzijiejie.app.R
import com.wanzijiejie.app.ui.screens.Area

/**
 * 批量分类对话框
 * 用于批量选择未分类商品的区域
 */
class BatchClassifyDialog(
    private val context: Context,
    private val areas: List<Area>,
    private val unclassifiedItems: List<Pair<String, String>>,
    private val onItemClassified: (String, Int) -> Unit,
    private val onAllClassified: () -> Unit
) {
    private val dialog: AlertDialog
    private val remainingItems = unclassifiedItems.map { it.first }.toMutableList()

    init {
        val builder = AlertDialog.Builder(context)
        builder.setTitle(context.getString(R.string.unclassified_items_title))

        // 创建自定义视图
        val inflater = LayoutInflater.from(context)
        val view = inflater.inflate(R.layout.dialog_batch_classify, null)

        // 设置对话框可取消
        builder.setCancelable(true)

        // 初始化视图组件
        val itemsListView = view.findViewById<ListView>(R.id.itemsListView)
        val areaSpinner = view.findViewById<Spinner>(R.id.areaSpinner)
        val confirmButton = view.findViewById<Button>(R.id.confirmButton)
        val cancelButton = view.findViewById<Button>(R.id.cancelButton)

        // 设置区域下拉列表
        val areaNames = areas.map { it.name }.toTypedArray()
        val adapter = ArrayAdapter(context, android.R.layout.simple_spinner_dropdown_item, areaNames)
        areaSpinner.adapter = adapter

        // 设置默认选择"其他"区域
        val otherIndex = areas.indexOfFirst { it.name == "其他" }
        if (otherIndex >= 0) {
            areaSpinner.setSelection(otherIndex)
        }

        // 设置商品列表为多选列表
        itemsListView.choiceMode = ListView.CHOICE_MODE_MULTIPLE

        // 创建商品列表适配器
        val itemsAdapter = ArrayAdapter(context, android.R.layout.simple_list_item_multiple_choice, remainingItems)
        itemsListView.adapter = itemsAdapter

        // 设置确认按钮点击事件
        confirmButton.setOnClickListener {
            val selectedAreaIndex = areaSpinner.selectedItemPosition

            // 获取选中的商品
            val selectedItems = mutableListOf<String>()
            for (i in 0 until itemsListView.count) {
                if (itemsListView.isItemChecked(i)) {
                    selectedItems.add(remainingItems[i])
                }
            }

            // 如果没有选中商品，则选中当前显示的第一个商品
            if (selectedItems.isEmpty() && remainingItems.isNotEmpty()) {
                selectedItems.add(remainingItems[0])
                // 选中第一个商品
                itemsListView.setItemChecked(0, true)
            }

            // 对每个选中的商品进行分类
            for (item in selectedItems) {
                // 打印分类信息，便于调试
                Toast.makeText(context, "将商品 '$item' 添加到 '${areas[selectedAreaIndex].name}' 区域", Toast.LENGTH_SHORT).show()

                // 回调通知分类结果
                onItemClassified(item, selectedAreaIndex)

                // 从剩余列表中移除已分类的商品
                remainingItems.remove(item)
            }

            // 更新商品列表
            itemsAdapter.clear()
            itemsAdapter.addAll(remainingItems)
            itemsAdapter.notifyDataSetChanged()

            // 只有当没有剩余商品时，才关闭对话框
            if (remainingItems.isEmpty()) {
                dialog.dismiss()
                onAllClassified()
            } else {
                // 清除所有选中状态，准备下一轮选择
                for (i in 0 until itemsListView.count) {
                    itemsListView.setItemChecked(i, false)
                }

                // 更新对话框标题，显示剩余未分类商品数量
                dialog.setTitle(context.getString(R.string.unclassified_items_remaining, remainingItems.size))

                // 打印剩余未分类商品信息，便于调试
                Toast.makeText(context, "还有 ${remainingItems.size} 个未分类商品", Toast.LENGTH_SHORT).show()

                // 强制刷新对话框，确保其不会关闭
                // 注意：这里不要创建新的对话框，而是使用现有的对话框
                if (!dialog.isShowing) {
                    dialog.show()
                }
            }
        }

        // 设置取消按钮点击事件
        cancelButton.setOnClickListener {
            // 将所有未分类的商品添加到"其他"区域
            if (remainingItems.isNotEmpty()) {
                val otherIndex = areas.indexOfFirst { it.name == "其他" }
                if (otherIndex >= 0) {
                    // 打印分类信息，便于调试
                    Toast.makeText(context, "将剩余 ${remainingItems.size} 个未分类商品添加到 '其他' 区域", Toast.LENGTH_SHORT).show()

                    // 创建副本以避免循环中修改集合
                    val itemsToProcess = remainingItems.toList()
                    for (item in itemsToProcess) {
                        onItemClassified(item, otherIndex)
                        remainingItems.remove(item)
                    }
                }
            }

            // 关闭对话框
            dialog.dismiss()
            onAllClassified()
        }

        builder.setView(view)
        dialog = builder.create()
    }

    /**
     * 显示对话框
     */
    fun show() {
        // 如果对话框已经显示，则不需要再次显示
        if (!dialog.isShowing) {
            dialog.show()
        }
    }
}
